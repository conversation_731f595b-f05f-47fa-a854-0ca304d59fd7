import { useCallback, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { CreateTicketFormData } from '../models/ticket-form.schema';

interface TicketDraft {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  department: 'sales' | 'support' | 'marketing' | 'technical';
  assignedTo?: string; // Single agent assignment
  cc: string[]; // Multiple users for CC
  // Note: attachments are not persisted to localStorage
}

const DRAFT_STORAGE_KEY = 'ticket-draft';

/**
 * Custom hook for managing ticket draft persistence
 * Automatically saves form data to localStorage and provides methods to load/clear drafts
 */
export function useDraftPersistence(form: UseFormReturn<CreateTicketFormData>) {
  // Save draft to localStorage (with error handling and SSR safety)
  const saveDraft = useCallback((data: TicketDraft) => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem(DRAFT_STORAGE_KEY, JSON.stringify(data));
      }
    } catch {
      // Silently fail if localStorage is not available
    }
  }, []);

  // Load draft from localStorage (with error handling and SSR safety)
  const loadDraft = useCallback((): TicketDraft | null => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const draftData = localStorage.getItem(DRAFT_STORAGE_KEY);
        return draftData ? JSON.parse(draftData) : null;
      }
      return null;
    } catch {
      return null;
    }
  }, []);

  // Clear draft from localStorage (with SSR safety)
  const clearDraft = useCallback(() => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem(DRAFT_STORAGE_KEY);
      }
    } catch {
      // Silently fail if localStorage is not available
    }
  }, []);

  // Load draft manually (opt-in, not automatic)
  const loadDraftIntoForm = useCallback(() => {
    const draft = loadDraft();
    if (draft) {
      // Reset form with draft data (attachments are handled separately)
      form.reset(draft);
      return true; // Indicates draft was loaded
    }
    return false; // No draft found
  }, [form, loadDraft]);

  // Watch form changes and save draft (debounced)
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const subscription = form.watch((data) => {
      // Clear previous timeout
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Only save if form has meaningful content (Title or Description)
      if (data.title || data.description) {
        const draftData: TicketDraft = {
          title: data.title || '',
          description: data.description || '',
          priority: data.priority || 'high',
          department: data.department || 'marketing',
          ...(data.assignedTo && { assignedTo: data.assignedTo }), // Only include if truthy
          cc: (data.cc || []).filter(
            (id): id is string => typeof id === 'string'
          ),
          // Don't include attachments in draft
        };

        // Debounce the save operation
        timeoutId = setTimeout(() => {
          saveDraft(draftData);
        }, 500);
      }
    });

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      subscription.unsubscribe();
    };
  }, [form, saveDraft]);

  return {
    saveDraft,
    loadDraft,
    loadDraftIntoForm,
    clearDraft,
  };
}
