'use client';

import { Badge } from '@/features/shared/components/ui/badge';
import { Button } from '@/features/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import { Play } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
  TicketPriority,
  Department,
  TicketWithDetails,
  AssignedUser,
} from '../models/ticket.schema';

// Type for ticket metadata that may contain assignedUser
interface TicketMetadata extends Record<string, unknown> {
  assignedUser?: AssignedUser;
}
import { useAuth } from '@/features/shared/hooks/useAuth';
import { InteractivePriorityBadge } from './InteractivePriorityBadge';
import { InteractiveDepartmentBadge } from './InteractiveDepartmentBadge';
import { statusColors, formatTicketStatus } from '../config/ticket-options';

interface TicketDetailHeaderProps {
  ticket: TicketWithDetails;
  isReplyDisabled: boolean;
  isOpeningTicket: boolean;
  handleOpenTicketInDetail: () => void;
  handlePriorityChange: (newPriority: TicketPriority) => void;
  handleDepartmentChange: (newDepartment: Department) => void;
}

export function TicketDetailHeader({
  ticket,
  isReplyDisabled,
  isOpeningTicket,
  handleOpenTicketInDetail,
  handlePriorityChange,
  handleDepartmentChange,
}: TicketDetailHeaderProps) {
  const { role } = useAuth();

  // Get assignedUser from ticket metadata or direct property
  const assignedUser =
    ticket.assignedUser || (ticket.metadata as TicketMetadata)?.assignedUser;

  return (
    <div className='p-6 border-b border-gray-200 dark:border-gray-700 shrink-0'>
      {/* Top row: Status, Priority, Department badges and Ticket actions */}
      <div className='flex items-center justify-between mb-3'>
        <div className='flex flex-wrap gap-2'>
          {ticket.status && (
            <Badge
              className={cn(
                'text-xs transition-all duration-300 ease-in-out',
                statusColors[ticket.status]
              )}
            >
              {formatTicketStatus(ticket.status)}
            </Badge>
          )}
          {ticket.metadata?.assignment?.auto_assigned &&
            (role === 'agent' ||
              role === 'admin' ||
              role === 'super_admin') && (
              <Badge
                variant='secondary'
                className='text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 transition-all duration-300 ease-in-out'
              >
                Auto Assigned
              </Badge>
            )}
          <InteractivePriorityBadge
            currentPriority={ticket.priority}
            onPriorityChange={handlePriorityChange}
          />
          <InteractiveDepartmentBadge
            currentDepartment={ticket.department}
            onDepartmentChange={handleDepartmentChange}
          />
        </div>
        {/* Hide ticket actions for users - only show for agents, admins, and super_admins */}
        {role !== 'user' && (
          <div className='flex gap-2 transition-all duration-300 ease-in-out'>
            {isReplyDisabled ? (
              <Button
                onClick={handleOpenTicketInDetail}
                disabled={isOpeningTicket}
                className='bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700 disabled:opacity-50 transition-all duration-200 ease-in-out'
              >
                <Play className='h-4 w-4 mr-2 transition-transform duration-200' />
                {isOpeningTicket ? 'Opening...' : 'Open This Ticket'}
              </Button>
            ) : (
              <Select defaultValue='ticket-actions'>
                <SelectTrigger className='w-40 transition-all duration-300 ease-in-out'>
                  <SelectValue placeholder='Ticket actions' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='ticket-actions'>Ticket actions</SelectItem>
                  <SelectItem value='close'>Close ticket</SelectItem>
                  <SelectItem value='assign'>Assign to agent</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        )}
      </div>

      {/* Ticket title - Remove bottom margin for users */}
      <h1
        className={`text-xl font-semibold text-gray-900 dark:text-gray-100 ${role !== 'user' ? 'mb-3' : ''}`}
      >
        {ticket.title}
      </h1>

      {/* Assignment information row - below title - Hidden from Users */}
      {role !== 'user' && (
        <div className='flex items-center justify-between'>
          {ticket.assignedTo && assignedUser ? (
            <div className='flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400'>
              <div className='flex items-center gap-2'>
                <span className='font-medium'>Assigned by:</span>
                <span>
                  {ticket.metadata?.assignment?.auto_assigned
                    ? ticket.metadata?.createdByUser?.name || ticket.userName
                    : ticket.metadata?.assignment?.assignedByUser?.name ||
                      'Rohit John'}
                </span>
                <Badge variant='outline' className='text-xs'>
                  {ticket.metadata?.assignment?.auto_assigned
                    ? ticket.metadata?.createdByUser?.role || 'User'
                    : ticket.metadata?.assignment?.assignedByUser?.role ||
                      'Super Admin'}
                </Badge>
                {/* Add assignment date */}
                {ticket.metadata?.assignment?.assigned_at && (
                  <span className='text-xs text-gray-500'>
                    on{' '}
                    {format(
                      new Date(ticket.metadata.assignment.assigned_at),
                      'MMM d, yyyy'
                    )}
                  </span>
                )}

                {ticket.assignedAt && (
                  <span className='text-xs text-gray-500'>
                    on {format(new Date(ticket.assignedAt), 'MMM d, yyyy')}
                  </span>
                )}
              </div>
            </div>
          ) : (
            <div className='text-sm text-gray-600 dark:text-gray-400'>
              <span className='font-medium'>Assign To (Admins & Agents)</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
