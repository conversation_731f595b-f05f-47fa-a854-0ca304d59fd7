'use client';

/**
 * FORCE REBUILD v3 - React Query Optimized Tickets Page - 2025 Implementation
 * Uses React Query for all server state management with minimal code footprint
 * DEBUGGING: This component should render and show console logs!
 */

import { useEffect } from 'react';
import dynamic from 'next/dynamic';
import { AppLayout } from '@/features/shared/components/AppLayout';
import { RecentTickets } from '@/features/ticketing/components/RecentTickets';
import { useAuth, usePermissions } from '@/features/shared/hooks/useAuth';
import { useSettingsSync } from '@/features/settings/hooks/useSettingsSync';
import { VisitorInformation } from '@/features/visitor/components/VisitorInformation';
import { useRealtimeTickets } from '@/hooks/useRealtimeQuery';
import {
  useRealtimeTicket,
  useTicketMessages,
  useUpdateTicket,
} from '@/hooks/useTickets';
import {
  useTicketingUISelectors,
  useTicketingUIActions,
} from '@/features/ticketing/store/use-ticketing-store';
import { useTicketWorkflow } from '@/features/ticketing/hooks/useTicketWorkflow';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';
import { ReplyDraftConfirmDialog } from '@/features/ticketing/components/ReplyDraftConfirmDialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/features/shared/components/ui/dialog';
import { Button } from '@/features/shared/components/ui/button';
// Tenant store temporarily disabled for SSR compatibility

import Link from 'next/link';

// Dynamic imports for better performance
const TicketDetail = dynamic(
  () =>
    import('@/features/ticketing/components/TicketDetail').then((mod) => ({
      default: mod.TicketDetail,
    })),
  {
    ssr: false,
  }
);

const CreateTicketForm = dynamic(
  () =>
    import('@/features/ticketing/components/CreateTicketForm').then((mod) => ({
      default: mod.CreateTicketForm,
    })),
  {
    ssr: false,
  }
);

function TicketsPageContent() {
  const { user, isLoaded, isSignedIn, isTokenReady, role, tenantId } =
    useAuth();
  const { hasPermission } = usePermissions();
  // Tenant store temporarily disabled for SSR compatibility
  const domainInfo = { isLocalhost: true }; // Temporary fallback

  // Note: isCreatingTicket state is now managed by useTicketWorkflow hook

  // Initialize settings sync
  useSettingsSync();

  // UI state management (Zustand for UI-only state)
  const selectedTicketId = useTicketingUISelectors.useSelectedTicketId();
  const setSelectedTicketId = useTicketingUIActions.useSetSelectedTicketId();
  const setCurrentTenantId = useTicketingUIActions.useSetCurrentTenantId();

  // CRITICAL FIX: Synchronize tenant ID with UI store for TicketDetail component
  useEffect(() => {
    if (tenantId && isLoaded && isSignedIn) {
      setCurrentTenantId(tenantId);
    }
  }, [tenantId, isLoaded, isSignedIn, setCurrentTenantId]);

  // Create role-based filter context for React Query
  const filterContext = {
    tenantId: tenantId || '',
    role: role || 'agent',
    userId: user?.id || '',
    email: user?.emailAddresses?.[0]?.emailAddress || '',
  };

  // Realtime tickets with proper auth checks
  const ticketsQuery = useRealtimeTickets(filterContext, {
    enabled: !!tenantId && isLoaded && isSignedIn && isTokenReady,
  });

  const selectedTicketQuery = useRealtimeTicket(
    tenantId || '',
    selectedTicketId || '',
    !!selectedTicketId && !!tenantId && isSignedIn && isTokenReady
  );

  const selectedTicketMessagesQuery = useTicketMessages(
    tenantId || '',
    selectedTicketId || '',
    !!selectedTicketId && !!tenantId && isTokenReady
  );

  // Mutations
  const updateTicketMutation = useUpdateTicket(tenantId || '');

  // Extract data from queries with proper loading state management
  const tickets = ticketsQuery.data || [];

  // CRITICAL FIX: Support optimistic tickets for instant detail page opening
  // If selectedTicketQuery returns null (for optimistic tickets), get the ticket from the list
  let selectedTicket = selectedTicketQuery.data || null;

  if (!selectedTicket && selectedTicketId) {
    // Check if it's an optimistic ticket in the tickets list
    const optimisticTicket = tickets.find(
      (ticket) => ticket.id === selectedTicketId
    );
    if (optimisticTicket) {
      selectedTicket = {
        ...optimisticTicket,
        status: optimisticTicket.status as
          | 'open'
          | 'closed'
          | 'new'
          | 'pending'
          | 'resolved',
        priority: optimisticTicket.priority as
          | 'low'
          | 'medium'
          | 'high'
          | 'urgent',
        department: optimisticTicket.department as
          | 'sales'
          | 'support'
          | 'technical',
      };
    }
  }

  // Loading state is now handled earlier in the component to prevent double loading screens

  // Loading state is now handled earlier in the component to prevent double loading screens

  // Get the correct ticket workflow with proper field transformation
  const {
    handleSubmitTicket,
    expandSection,
    showDraftConfirmation,
    showReplyDraftConfirmation,
    isCreatingTicket: workflowIsCreatingTicket,
    handleTicketSelect: workflowHandleTicketSelect,
    handleCreateTicket: workflowHandleCreateTicket,
    handleCancelCreateTicket,
    handleSaveChanges,
    handleDiscardChanges,
    handleDialogClose,
    handleReplyDraftSave,
    handleReplyDraftDiscard,
    handleReplyDraftCancel,
  } = useTicketWorkflow(
    (tickets || []) as Ticket[],
    tenantId,
    true, // isCacheLoaded
    true // hasInitialApiLoad
  );

  // CRITICAL FIX: Enhanced loading state detection for proper skeleton loading behavior
  // Distinguish between different loading scenarios for optimal UX
  const isLoadingSelectedTicket = selectedTicketQuery.isFetching;
  const isLoadingSelectedMessages = selectedTicketMessagesQuery.isFetching;

  // Check if we're showing cached data while fetching fresh data
  const isShowingCachedTicket = selectedTicketQuery.isPlaceholderData;
  const isShowingCachedMessages = selectedTicketMessagesQuery.isPlaceholderData;

  // Determine if we should show skeleton loading:
  // - Show skeleton when fetching non-cached data (first time or after cache expiry)
  // - Don't show skeleton when we have cached data and are just refreshing in background
  const shouldShowTicketSkeleton =
    (isLoadingSelectedTicket && !selectedTicket && !isShowingCachedTicket) ||
    (isLoadingSelectedMessages &&
      !selectedTicketMessagesQuery.data &&
      !isShowingCachedMessages);

  // Enhanced loading detection to prevent placeholder flash during transitions
  const isTransitioning =
    selectedTicketId && !selectedTicket && !shouldShowTicketSkeleton;

  // Use workflow handlers and state that include draft management
  const handleTicketSelect = workflowHandleTicketSelect;
  const handleCreateTicket = workflowHandleCreateTicket;
  const isCreatingTicket = workflowIsCreatingTicket;

  // Remove conflicting agent logic - let useTicketWorkflow handle this

  // CRITICAL FIX: Single loading state to prevent double loading screens
  if (!isLoaded || !isSignedIn) {
    return null; // Let SessionValidator handle authentication redirects
  }

  // For localhost, show welcome message if not authenticated
  if (domainInfo?.isLocalhost && !user) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <div className='max-w-md w-full space-y-8 p-8 text-center'>
          <h1 className='text-4xl font-bold text-gray-900 mb-4'>
            Welcome to QuantumNest
          </h1>
          <p className='text-lg text-gray-600 mb-8'>
            This is a multi-tenant support ticketing system. To access tickets,
            please visit your organization&apos;s subdomain or sign in.
          </p>
          <Link
            href='/sign-in'
            className='inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors'
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  // For subdomains, authentication is required (handled by middleware)
  // If we reach here, user is authenticated and ready to show dashboard

  return (
    <AppLayout rightSidebar={<VisitorInformation />}>
      <div className='flex h-full'>
        {/* Recent Tickets Sidebar */}
        <div className='w-96 shrink-0 h-full flex flex-col'>
          <div className='flex-1'>
            <RecentTickets
              selectedTicketId={selectedTicketId}
              onTicketSelect={handleTicketSelect}
              onCreateTicket={handleCreateTicket}
              tickets={tickets as Ticket[]}
              isLoading={false} // Loading handled at page level now
              expandSection={expandSection}
            />
          </div>
        </div>
        {/* Main Content - Ticket Detail or Create Form */}
        <div className='flex-1 p-6 h-full overflow-hidden'>
          {/* CRITICAL FIX: Persistent white container to prevent background flashing during transitions */}
          <div className='h-full rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm shadow-gray-200/50 dark:shadow-gray-900/50 flex flex-col'>
            {selectedTicketId ? (
              // CRITICAL FIX: Always prioritize showing detail page when we have a selected ticket
              // This handles the optimistic UI case where selectedTicketId is set but isCreatingTicket might still be true
              selectedTicket ? (
                <TicketDetail
                  ticket={selectedTicket}
                  onTicketUpdate={(ticketId, updates) =>
                    updateTicketMutation.mutate({ ticketId, updates })
                  }
                />
              ) : (
                // Show loading state while optimistic ticket is being resolved
                <TicketDetail ticket={null} onTicketUpdate={() => {}} />
              )
            ) : isCreatingTicket ? (
              <CreateTicketForm
                onSubmit={async (data) => {
                  // CRITICAL FIX: Let TanStack Query mutation handle optimistic UI entirely
                  // This prevents duplicate optimistic entries and unnecessary loading states
                  try {
                    await handleSubmitTicket(data);
                    // handleSubmitTicket already handles the optimistic UI and ticket selection
                  } catch (error) {
                    // Error handling is already done in handleSubmitTicket
                    console.error('Failed to create ticket:', error);
                  }
                }}
                onDiscard={() => {
                  // Clear selection when discarding create form
                  setSelectedTicketId(null);
                  handleCancelCreateTicket();
                }}
                isSubmitting={false}
                tenantId={tenantId || undefined}
              />
            ) : shouldShowTicketSkeleton || isTransitioning ? (
              <TicketDetail ticket={null} onTicketUpdate={() => {}} />
            ) : (
              // Placeholder - only shown for agents with no tickets
              <div className='flex-1 flex items-center justify-center'>
                <div className='text-center'>
                  <p className='text-gray-500 dark:text-gray-400 mb-2'>
                    {tickets.length === 0
                      ? role === 'agent'
                        ? 'No tickets assigned to you'
                        : 'No tickets available'
                      : role === 'agent'
                        ? 'Select a ticket to view details'
                        : 'Click "Create New Ticket" to get started'}
                  </p>
                  {tickets.length === 0 && !hasPermission('tickets.create') && (
                    <p className='text-sm text-gray-400 dark:text-gray-500'>
                      Contact your administrator to get tickets assigned to you
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Draft Confirmation Dialog for Ticket Creation */}
      <Dialog open={showDraftConfirmation} onOpenChange={handleDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Unsaved Draft</DialogTitle>
            <DialogDescription>
              You have unsaved changes in your ticket draft. What would you like
              to do?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className='gap-2'>
            <Button variant='outline' onClick={handleDiscardChanges}>
              Discard Changes
            </Button>
            <Button onClick={handleSaveChanges}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reply Draft Confirmation Dialog */}
      <ReplyDraftConfirmDialog
        open={showReplyDraftConfirmation}
        onOpenChange={handleReplyDraftCancel}
        onDiscard={handleReplyDraftDiscard}
        onSave={handleReplyDraftSave}
      />
    </AppLayout>
  );
}

export default function TicketsPage() {
  return <TicketsPageContent />;
}
