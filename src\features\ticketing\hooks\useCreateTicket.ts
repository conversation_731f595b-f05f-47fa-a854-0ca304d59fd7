import { useState, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  CreateTicketFormSchema,
  CreateTicketFormData,
} from '../models/ticket-form.schema';
import { useDraftPersistence } from './useDraftPersistence';
import { toast } from '@/features/shared/components/toast';
import { UploadedFile } from '@/features/shared/components/DynamicFileUpload';
import { Department } from '../models/ticket.schema';

/**
 * Validates if auto-assignment is available for the given department and tenant
 */
async function validateAutoAssignment(
  tenantId: string,
  department: Department
): Promise<boolean> {
  try {
    const response = await fetch('/api/auto-assignment/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tenant_id: tenantId,
        department,
      }),
    });

    if (!response.ok) {
      return false;
    }

    const result = await response.json();
    return result.canAutoAssign || false;
  } catch (error) {
    console.error('Auto-assignment validation error:', error);
    return false;
  }
}

/**
 * Hook for managing the create ticket form.
 *
 * @param tenantId - The ID of the current tenant.
 * @param onSubmit - The function to call when the form is submitted.
 * @param userRole - The role of the current user (for role-based validation).
 * @returns An object with the form, state, and handlers.
 */
export function useCreateTicket(
  tenantId: string | null,
  onSubmit: (
    data: CreateTicketFormData & { attachment_ids: string[] }
  ) => Promise<void>,
  userRole?: string
) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const form = useForm<CreateTicketFormData>({
    resolver: zodResolver(CreateTicketFormSchema),
    defaultValues: {
      title: '',
      description: '',
      priority: 'high',
      department: 'marketing',
      assignedTo: undefined,
      cc: [],
    },
  });

  const { clearDraft, loadDraftIntoForm } = useDraftPersistence(form);

  // Load draft when component mounts
  useEffect(() => {
    loadDraftIntoForm();
  }, [loadDraftIntoForm]);

  const handleSubmit = useCallback(
    async (data: CreateTicketFormData) => {
      if (!tenantId) {
        toast.error('Error', {
          description: 'Tenant information not available',
          duration: 4000,
        });
        return;
      }

      try {
        // Only validate assignment for admin/super_admin roles
        // Regular users should be able to create tickets without assignment constraints
        if (
          !data.assignedTo &&
          (userRole === 'admin' || userRole === 'super_admin')
        ) {
          const canAutoAssign = await validateAutoAssignment(
            tenantId,
            data.department
          );
          if (!canAutoAssign) {
            toast.error('Assignment Required', {
              description:
                'No auto-assignment rules are configured for this department and no default agent is set. Please assign the ticket to an agent manually.',
              duration: 4000,
            });
            return;
          }
        }

        clearDraft();

        // Use the provided onSubmit callback instead of calling createTicket directly
        await onSubmit({ ...data, attachment_ids: [] });
        form.reset();
        setUploadedFiles([]);
      } catch (error) {
        console.error('Failed to create ticket:', error);
        // Toast notification is handled by the parent component (useTicketWorkflow)
        // to avoid duplicate notifications
        throw error; // Re-throw to let parent handle the error
      }
    },
    [clearDraft, form, tenantId, onSubmit, userRole]
  );

  const handleDiscard = useCallback(() => {
    clearDraft();
    setUploadedFiles([]);
    form.reset();
  }, [clearDraft, form]);

  return {
    form,
    uploadedFiles,
    setUploadedFiles,
    handleSubmit,
    handleDiscard,
  };
}
