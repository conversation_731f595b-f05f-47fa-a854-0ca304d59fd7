/**
 * Performance Tests for VirtualizedTicketList - 2025 Best Practices
 *
 * Tests the optimized React Window implementation with real-time integration
 * and responsive design features.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { VirtualizedTicketList } from '../VirtualizedTicketList';
import { Ticket } from '../../models/ticket.schema';

// Mock react-window components
jest.mock('react-window', () => ({
  FixedSizeList: ({ children, itemCount, itemData, overscanCount }: any) => {
    // Simulate virtualization by only rendering visible items
    const visibleItems = Math.min(itemCount, 10); // Simulate 10 visible items

    return (
      <div data-testid='virtualized-list' data-overscan={overscanCount}>
        {Array.from({ length: visibleItems }, (_, index) =>
          children({ index, style: {}, data: itemData })
        )}
      </div>
    );
  },
}));

jest.mock('react-virtualized-auto-sizer', () => ({
  __esModule: true,
  default: ({ children }: any) => children({ height: 600, width: 400 }),
}));

// Generate test tickets
const generateTestTickets = (count: number): Ticket[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `ticket-${index}`,
    tenantId: 'test-tenant',
    title: `Test Ticket ${index}`,
    description: `Description for ticket ${index}`,
    status: 'open' as const,
    priority: 'medium' as const,
    department: 'support' as const,
    createdAt: new Date(),
    updatedAt: new Date(),
    userId: 'user-1',
    userName: 'Test User',
    userEmail: '<EMAIL>',
    messages: [],
    attachments: [],
  }));
};

describe('VirtualizedTicketList Performance Tests', () => {
  const mockOnTicketSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('2025 Best Practices Implementation', () => {
    test('uses optimal overscan count for smooth scrolling', async () => {
      const tickets = generateTestTickets(50); // Above threshold

      render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      const virtualizedList = screen.getByTestId('virtualized-list');
      expect(virtualizedList).toHaveAttribute('data-overscan', '5');
    });

    test('uses regular rendering for small lists (below threshold)', () => {
      const tickets = generateTestTickets(15); // Below threshold of 20

      render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      // Should not use virtualization
      expect(screen.queryByTestId('virtualized-list')).not.toBeInTheDocument();

      // Should render all tickets directly
      tickets.forEach((ticket) => {
        expect(screen.getByText(ticket.title)).toBeInTheDocument();
      });
    });

    test('uses virtualization for large lists (above threshold)', () => {
      const tickets = generateTestTickets(25); // Above threshold of 20

      render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      // Should use virtualization
      expect(screen.getByTestId('virtualized-list')).toBeInTheDocument();
    });
  });

  describe('Responsive Design Optimization', () => {
    test('handles responsive width calculation', () => {
      const tickets = generateTestTickets(25);

      render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      // AutoSizer mock provides width: 400, which should be optimized to MIN_SIDEBAR_WIDTH (320)
      expect(screen.getByTestId('virtualized-list')).toBeInTheDocument();
    });
  });

  describe('Real-time Integration', () => {
    test('logs real-time integration activation for large lists', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const tickets = generateTestTickets(25);

      render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      expect(consoleSpy).toHaveBeenCalledWith(
        '🔄 VirtualizedTicketList: Real-time integration active for',
        25,
        'tickets'
      );

      consoleSpy.mockRestore();
    });

    test('does not activate real-time integration for small lists', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const tickets = generateTestTickets(15);

      render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      expect(consoleSpy).not.toHaveBeenCalledWith(
        expect.stringContaining('Real-time integration active')
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Performance Characteristics', () => {
    test('renders large lists efficiently', () => {
      const startTime = performance.now();
      const tickets = generateTestTickets(1000);

      render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render quickly even with 1000 tickets due to virtualization
      expect(renderTime).toBeLessThan(100); // Less than 100ms
      expect(screen.getByTestId('virtualized-list')).toBeInTheDocument();
    });

    test('maintains stable performance with ticket updates', async () => {
      let tickets = generateTestTickets(50);

      const { rerender } = render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      // Simulate real-time update by adding a new ticket at the beginning
      const newTicket = generateTestTickets(1)[0];
      newTicket.id = 'new-ticket';
      newTicket.title = 'New Real-time Ticket';
      tickets = [newTicket, ...tickets];

      const startTime = performance.now();

      rerender(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      const endTime = performance.now();
      const updateTime = endTime - startTime;

      // Should update quickly due to virtualization
      expect(updateTime).toBeLessThan(50); // Less than 50ms
    });
  });

  describe('Edge Cases', () => {
    test('handles empty ticket list', () => {
      render(
        <VirtualizedTicketList
          tickets={[]}
          onTicketSelect={mockOnTicketSelect}
          tenantId='test-tenant'
        />
      );

      expect(screen.getByText('No tickets found')).toBeInTheDocument();
    });

    test('handles skeleton loading state', () => {
      render(
        <VirtualizedTicketList
          tickets={[]}
          onTicketSelect={mockOnTicketSelect}
          showSkeleton={true}
          tenantId='test-tenant'
        />
      );

      // Should show skeleton instead of empty state
      expect(screen.queryByText('No tickets found')).not.toBeInTheDocument();
    });

    test('handles missing tenantId gracefully', () => {
      const tickets = generateTestTickets(25);

      render(
        <VirtualizedTicketList
          tickets={tickets}
          onTicketSelect={mockOnTicketSelect}
          // No tenantId provided
        />
      );

      // Should still render but without real-time integration
      expect(screen.getByTestId('virtualized-list')).toBeInTheDocument();
    });
  });
});
