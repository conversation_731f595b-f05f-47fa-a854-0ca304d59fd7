import { useEffect, useMemo } from 'react';
import {
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from '@tanstack/react-query';
import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import type { Database } from '@/types/supabase';
import RealtimeDataService from '@/lib/services/realtime-data.service';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';

// Type for database row
type TicketRow = Database['public']['Tables']['tickets']['Row'];
type MessageRow = Database['public']['Tables']['ticket_messages']['Row'];

/**
 * ✅ Recommended Pattern: useRealtimeQuery() Custom Hook (Modern Supabase + React Query)
 *
 * Based on latest 2025 patterns from Docs/LatestPatterns.md
 *
 * This hook encapsulates:
 * - Fetching data with React Query
 * - Subscribing to Supabase real-time changes
 * - Automatically updating the cache when DB changes occur
 *
 * Benefits:
 * - Minimal, modern, robust
 * - Clean separation of concerns
 * - No manual subscription management
 * - Automatic cache updates on real-time events
 */
export function useRealtimeQuery<T extends { id: string }>(
  queryKey: string[],
  fetchFn: () => Promise<T[]>,
  table: string,
  options?: {
    filter?: string;
    schema?: string;
    queryOptions?: Omit<UseQueryOptions<T[]>, 'queryKey' | 'queryFn'>;
  }
) {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // Initialize RealtimeDataService for proper user data transformation
  const realtimeDataService = useMemo(
    () => new RealtimeDataService(supabase),
    [supabase]
  );

  // Memoize queryKey to prevent subscription churn
  const stableQueryKey = useMemo(() => queryKey, [queryKey]);

  // Memoize options to prevent subscription churn
  const stableOptions = useMemo(() => options, [options]);

  // React Query for data fetching
  const query = useQuery({
    queryKey: stableQueryKey,
    queryFn: fetchFn,
    ...stableOptions?.queryOptions,
  });

  // ✅ Simple real-time subscription following recommended pattern
  useEffect(() => {
    if (!user) return;

    const channel = supabase
      .channel(`realtime:${table}`)
      .on(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        'postgres_changes' as any,
        {
          event: '*',
          schema: stableOptions?.schema || 'public',
          table,
          filter: stableOptions?.filter,
        },
        async (
          payload: RealtimePostgresChangesPayload<Record<string, unknown>>
        ) => {
          console.log('Real-time change received:', payload);

          const { eventType, new: newRow, old: oldRow } = payload;

          // Skip updates for current user's own actions to prevent duplicates
          if (userDatabaseId && eventType === 'INSERT') {
            if (table === 'tickets') {
              const ticketRow = newRow as TicketRow;
              if (ticketRow.created_by === userDatabaseId) return;
            } else if (table === 'ticket_messages') {
              const messageRow = newRow as MessageRow;
              if (messageRow.author_id === userDatabaseId) return;
            }
          }

          try {
            // Transform data first (async operations)
            let transformedRow: T | null = null;

            if (eventType === 'INSERT' || eventType === 'UPDATE') {
              if (table === 'tickets') {
                const transformed =
                  await realtimeDataService.transformTicketRow(
                    newRow as TicketRow
                  );
                transformedRow = transformed as unknown as T;
              } else if (table === 'ticket_messages') {
                const transformed =
                  await realtimeDataService.transformMessageRow(
                    newRow as MessageRow
                  );
                transformedRow = transformed as unknown as T;
              } else {
                transformedRow = newRow as T;
              }
            }

            // Update cache (sync operation)
            queryClient.setQueryData(stableQueryKey, (oldData: T[] = []) => {
              switch (eventType) {
                case 'INSERT': {
                  if (!transformedRow) return oldData;

                  // Check for duplicates
                  const existingIndex = oldData.findIndex(
                    (item) => item.id === transformedRow.id
                  );
                  if (existingIndex !== -1) {
                    const newData = [...oldData];
                    newData[existingIndex] = transformedRow;
                    return newData;
                  }

                  // Insert new item
                  return table === 'ticket_messages'
                    ? [...oldData, transformedRow] // Messages: append
                    : [transformedRow, ...oldData]; // Tickets: prepend
                }

                case 'UPDATE': {
                  if (!transformedRow) return oldData;
                  return oldData.map((item) =>
                    item.id === transformedRow.id ? transformedRow : item
                  );
                }

                case 'DELETE':
                  return oldData.filter((item) => item.id !== (oldRow as T).id);

                default:
                  return oldData;
              }
            });
          } catch (error) {
            console.error('Real-time update error:', error);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [
    supabase,
    stableQueryKey,
    table,
    stableOptions,
    user,
    userDatabaseId,
    queryClient,
    realtimeDataService,
  ]);

  return query;
}

// Import types needed for tickets
interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
  email?: string;
}

interface TicketFilterOptions {
  status?: string;
  priority?: string;
  assignedTo?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Import types needed for tickets
interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
  email?: string;
}

interface TicketFilterOptions {
  status?: string;
  priority?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  roleFilter?: 'new' | 'assigned' | 'all';
}

/**
 * Hook to resolve tenant UUID from subdomain
 */
export function useTenantUuid(tenantId: string) {
  const { supabase } = useSupabaseClient();

  return useQuery({
    queryKey: ['tenant-uuid', tenantId],
    queryFn: async () => {
      // If already a UUID, return it
      if (
        tenantId.match(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        )
      ) {
        return tenantId;
      }

      // Convert subdomain to UUID
      const { data: tenantData, error: tenantError } = await supabase
        .from('tenants')
        .select('id')
        .eq('subdomain', tenantId)
        .single();

      if (tenantError || !tenantData) {
        throw new Error(`Tenant '${tenantId}' not found`);
      }

      return tenantData.id;
    },
    staleTime: 1000 * 60 * 60, // 1 hour - tenant UUIDs rarely change
    gcTime: 1000 * 60 * 60 * 24, // 24 hours
    enabled: !!tenantId,
  });
}

/**
 * Specialized hook for tickets with real-time updates
 * Compatible with useTickets signature for drop-in replacement
 */
export function useRealtimeTickets(
  context: RoleBasedFilterContext,
  options?: TicketFilterOptions & { enabled?: boolean }
) {
  const { supabase } = useSupabaseClient();
  const { enabled = true, ...filterOptions } = options || {};

  // Resolve tenant UUID first
  const tenantUuidQuery = useTenantUuid(context.tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Memoize filterOptions to prevent subscription churn
  const stableFilterOptions = useMemo(() => filterOptions, [filterOptions]);

  // Extract complex expression for dependency array
  const stableFilterOptionsString = JSON.stringify(stableFilterOptions || {});

  // Memoize queryKey to prevent subscription churn
  const queryKey = useMemo(
    () => [
      'tickets',
      tenantUuid || 'loading',
      'list',
      stableFilterOptionsString,
    ],
    [tenantUuid, stableFilterOptionsString]
  );

  return useRealtimeQuery(
    queryKey,
    async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      console.log('🔍 Fetching tickets for tenant UUID:', tenantUuid);

      // Build the same query as the original API with specific foreign key relationships
      let query = supabase
        .from('tickets')
        .select(
          `
          *,
          users!tickets_created_by_fkey (
            id,
            clerk_id,
            first_name,
            last_name,
            email,
            role,
            avatar_url
          ),
          assigned_user:users!tickets_assigned_to_fkey (
            id,
            clerk_id,
            first_name,
            last_name,
            email,
            role,
            avatar_url
          )
        `
        )
        .eq('tenant_id', tenantUuid)
        .order('created_at', { ascending: false });

      // Apply role-based filtering
      if (stableFilterOptions?.roleFilter === 'new') {
        query = query.is('assigned_to', null);
      } else if (stableFilterOptions?.roleFilter === 'assigned') {
        // CRITICAL FIX: For assigned tickets, fetch all assigned tickets and let frontend filtering handle role-based logic
        // The assignment metadata (assigned_by) is stored in JSON field, making server-side filtering complex
        if (context.role === 'admin' || context.role === 'super_admin') {
          // Both admin and super_admin see all assigned tickets, frontend filtering will handle the specifics
          query = query.not('assigned_to', 'is', null);
        } else if (context.role === 'agent' && context.userId) {
          // Agents only see tickets assigned to them (using database UUID)
          query = query.eq('assigned_to', context.userId);
        }
      }

      // Apply status filtering
      if (stableFilterOptions?.status) {
        query = query.eq('status', stableFilterOptions.status);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Error fetching tickets:', error);
        throw error;
      }

      console.log('✅ Fetched tickets:', data?.length || 0);

      // Transform raw database data to match Ticket schema format
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const transformedTickets = (data || []).map((ticket: any) => ({
        id: ticket.id as string,
        tenantId: context.tenantId,
        title: ticket.title as string,
        description: ticket.description as string,
        status: ticket.status as string,
        priority: ticket.priority as string,
        department: ticket.department as string,
        createdAt: new Date(ticket.created_at as string),
        updatedAt: new Date(ticket.updated_at as string),
        userId: ticket.created_by as string,
        userName: ticket.users
          ? `${ticket.users.first_name || ''} ${ticket.users.last_name || ''}`.trim() ||
            'Unknown User'
          : 'Unknown User',
        userEmail: ticket.users?.email || '<EMAIL>',
        userAvatar: ticket.users?.avatar_url,
        messages: [],
        attachments: [],
        assignedTo: ticket.assigned_to as string | undefined,
        assignedUser: ticket.assigned_user
          ? {
              id: ticket.assigned_user.id,
              name: `${ticket.assigned_user.first_name || ''} ${ticket.assigned_user.last_name || ''}`.trim(),
              email: ticket.assigned_user.email,
              role: ticket.assigned_user.role,
              avatar: ticket.assigned_user.avatar_url,
            }
          : null,
        dueDate: ticket.due_date
          ? new Date(ticket.due_date as string)
          : undefined,
        resolvedAt: ticket.resolved_at
          ? new Date(ticket.resolved_at as string)
          : undefined,
        closedAt: ticket.closed_at
          ? new Date(ticket.closed_at as string)
          : undefined,
        tags: ticket.tags || [],
        metadata: ticket.metadata || {},
      }));

      return transformedTickets;
    },
    'tickets',
    {
      filter: tenantUuid
        ? `tenant_id=eq.${tenantUuid}`
        : 'tenant_id=eq.placeholder',
      queryOptions: {
        staleTime: 1000 * 60 * 5, // 5 minutes
        gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
        enabled: enabled && !!tenantUuid && !tenantUuidQuery.isLoading,
      },
    }
  );
}

/**
 * Interface for test entries
 */
export interface TestEntry {
  id: string;
  title: string;
  description: string;
  created_at: string;
  tenant_id: string;
}

/**
 * Specialized hook for the test table
 */
export function useRealtimeTest(tenantId: string) {
  const { supabase } = useSupabaseClient();

  return useRealtimeQuery<TestEntry>(
    ['realtime-test', tenantId],
    async () => {
      console.log('🔍 Fetching test entries for tenant:', tenantId);
      // Use type assertion to bypass TypeScript schema inference issues
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase as any)
        .from('realtime_test')
        .select('id, title, description, created_at, tenant_id')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching test entries:', error);
        throw error;
      }

      console.log('✅ Fetched test entries:', data?.length || 0);
      return (data || []) as TestEntry[];
    },
    'realtime_test',
    {
      filter: `tenant_id=eq.${tenantId}`,
      queryOptions: {
        staleTime: 0, // Always fresh for testing
        gcTime: 1000 * 60 * 5, // 5 minutes
      },
    }
  );
}
