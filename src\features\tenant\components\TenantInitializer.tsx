'use client';

import { useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useTenantActions } from '../store/use-tenant-store';
import { useClerkSupabaseSync } from '@/hooks/useClerkSupabaseSync';
import { getDomainFromWindow } from '@/lib/domain';

export function TenantInitializer() {
  const { isLoaded, isSignedIn } = useUser();
  const { initializeDomain } = useTenantActions();

  // Get tenant ID from domain for sync
  const tenantId =
    typeof window !== 'undefined' ? getDomainFromWindow(window).tenantId : null;

  // Initialize Clerk-Supabase sync when user is authenticated
  const syncStatus = useClerkSupabaseSync(tenantId);

  useEffect(() => {
    if (isLoaded) {
      // Initialize domain info only - server state handled by React Query
      initializeDomain();
    }
  }, [isLoaded, initializeDomain]);

  // Log sync status for debugging (remove in production) - optimized to reduce console spam
  useEffect(() => {
    if (isSignedIn && tenantId && syncStatus && !syncStatus.isLoading) {
      // Only log when sync status changes, not during loading
      console.log('🔄 Clerk-Supabase sync status:', {
        tenantId,
        needsSync: syncStatus.needsSync,
        isLoading: syncStatus.isLoading,
        isComplete: syncStatus.isComplete,
        error: syncStatus.error,
      });
    }
  }, [
    isSignedIn,
    tenantId,
    syncStatus.isComplete,
    syncStatus.needsSync,
    syncStatus.error,
  ]);

  return null;
}
