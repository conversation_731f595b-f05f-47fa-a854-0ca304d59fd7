import { useState, useCallback, useEffect, useMemo } from 'react';
import { useUser } from '@clerk/nextjs';
import {
  useTicketingUISelectors,
  useTicketingUIActions,
} from '@/features/ticketing/store/use-ticketing-store';
import { usePermissions, useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import { useTenantStore } from '@/features/tenant/store/use-tenant-store';
import { toast } from '@/features/shared/components/toast';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';
import { CreateTicketFormData } from '@/features/ticketing/models/ticket-form.schema';
import { useCategorizedTickets } from '@/features/ticketing/hooks/useCategorizedTickets';
import { countWords } from '@/lib/utils';
import { useReplyDraftPersistence } from './useReplyDraftPersistence';
import { useCreateTicket } from '@/hooks/useTickets';

/**
 * Manages the user-facing workflow of the ticketing page.
 * This includes ticket selection, creation, and draft management.
 *
 * @param tickets - The list of tickets to be managed.
 * @param tenantId - The ID of the current tenant.
 * @param isCacheLoaded - Flag indicating if the cache is loaded.
 * @param hasInitialApiLoad - Flag indicating if the initial API load is complete.
 * @returns An object with state and handlers for the ticketing workflow.
 */
export function useTicketWorkflow(
  tickets: Ticket[],
  tenantId: string | null,
  isCacheLoaded: boolean,
  hasInitialApiLoad: boolean
) {
  const { user: clerkUser } = useUser();
  const { user, role } = useAuth();
  const { hasPermission } = usePermissions();
  const { userDatabaseId } = useUserDatabaseId();
  const currentTenantId = useTenantStore((state) => state.tenantId);

  const selectedTicketId = useTicketingUISelectors.useSelectedTicketId();
  const setSelectedTicketId = useTicketingUIActions.useSetSelectedTicketId();

  // React Query mutation for ticket creation with optimistic updates
  const createTicketMutation = useCreateTicket(
    currentTenantId || tenantId || '',
    (optimisticTicketId: string) => {
      // CRITICAL FIX: Immediately select optimistic ticket for instant UI feedback
      setSelectedTicketId(optimisticTicketId);
      setIsCreatingTicket(false);
    },
    (realTicketId: string, optimisticTicketId: string) => {
      // CRITICAL FIX: Smoothly transition from optimistic to real ticket ID
      // Only update selection if the optimistic ticket is currently selected
      if (selectedTicketId === optimisticTicketId) {
        setSelectedTicketId(realTicketId);
      }
    }
  );

  const [isCreatingTicket, setIsCreatingTicket] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDraftConfirmation, setShowDraftConfirmation] = useState(false);
  const [pendingTicketId, setPendingTicketId] = useState<string | null>(null);
  const [justCreatedTicket, setJustCreatedTicket] = useState(false);
  const [showReplyDraftConfirmation, setShowReplyDraftConfirmation] =
    useState(false);
  const [pendingReplyNavigation, setPendingReplyNavigation] = useState<
    string | null
  >(null);
  const [pendingCreateTicket, setPendingCreateTicket] = useState(false);
  const [expandSection, setExpandSection] = useState<
    'new' | 'open' | 'closed' | null
  >(null);

  // Draft persistence hook
  const { hasValidContent, loadDraft, clearDraft } = useReplyDraftPersistence();

  const selectedTicket = useMemo(
    () => tickets.find((ticket) => ticket.id === selectedTicketId),
    [tickets, selectedTicketId]
  );

  // Get categorized tickets for role-based selection
  const categorizedTickets = useCategorizedTickets(tickets);

  // Helper function to determine which section a ticket belongs to
  const getTicketSection = useCallback(
    (ticketId: string): 'new' | 'open' | 'closed' => {
      const ticket = tickets.find((t) => t.id === ticketId);
      if (!ticket) return 'open'; // Default fallback

      // Check which section the ticket belongs to based on categorized tickets
      if (categorizedTickets.newTickets.some((t) => t.id === ticketId)) {
        return 'new';
      } else if (
        categorizedTickets.closedTickets.some((t) => t.id === ticketId)
      ) {
        return 'closed';
      } else {
        return 'open'; // Default to open section for assigned/open tickets
      }
    },
    [tickets, categorizedTickets]
  );

  // CRITICAL FIX: Auto-expand section when selected ticket moves to a different section
  useEffect(() => {
    if (selectedTicketId && !isCreatingTicket) {
      const currentTicketSection = getTicketSection(selectedTicketId);
      if (currentTicketSection !== expandSection) {
        setExpandSection(currentTicketSection);
      }
    }
  }, [
    selectedTicketId,
    tickets,
    getTicketSection,
    expandSection,
    isCreatingTicket,
  ]);

  // Agent-specific default ticket selection logic
  useEffect(() => {
    if (
      !isCacheLoaded ||
      !hasInitialApiLoad ||
      selectedTicketId ||
      isCreatingTicket ||
      justCreatedTicket
    ) {
      return;
    }

    // Agent-specific auto-selection logic
    if (role === 'agent') {
      const newCount = categorizedTickets.newTickets.length;
      const openCount = categorizedTickets.openTickets.length;

      if (newCount > 0) {
        // Priority 1: New tickets available - select most recent
        const firstTicket = categorizedTickets.newTickets[0];
        if (firstTicket) {
          setSelectedTicketId(firstTicket.id);
        }
        setExpandSection('new');
      } else if (openCount > 0) {
        // Priority 2: No new tickets but open tickets available - select most recent open
        const firstOpenTicket = categorizedTickets.openTickets[0];
        if (firstOpenTicket) {
          setSelectedTicketId(firstOpenTicket.id);
        }
        setExpandSection('open');
      } else {
        // Priority 3: No tickets available - show default blank page
        setSelectedTicketId(null);
        setIsCreatingTicket(false);
      }
    } else {
      // For all other users: Default to create ticket form if they have permission
      setSelectedTicketId(null);
      if (hasPermission('tickets.create')) {
        setIsCreatingTicket(true);
      } else {
        setIsCreatingTicket(false);
      }
    }
  }, [
    categorizedTickets,
    selectedTicketId,
    isCreatingTicket,
    justCreatedTicket,
    setSelectedTicketId,
    isCacheLoaded,
    hasInitialApiLoad,
    role,
    hasPermission,
  ]);

  const handleCreateTicket = useCallback(() => {
    if (hasPermission('tickets.create')) {
      // CRITICAL FIX: Check for reply draft before navigating to create form
      if (selectedTicketId) {
        const currentReplyDraft = loadDraft(selectedTicketId);
        if (currentReplyDraft && hasValidContent(currentReplyDraft)) {
          setPendingCreateTicket(true);
          setShowReplyDraftConfirmation(true);
          return;
        }
      }
      setIsCreatingTicket(true);
      setJustCreatedTicket(false);
      setSelectedTicketId(null);
    }
  }, [
    hasPermission,
    setSelectedTicketId,
    selectedTicketId,
    loadDraft,
    hasValidContent,
  ]);

  const handleCancelCreateTicket = useCallback(() => {
    setIsCreatingTicket(false);
  }, []);

  const handleTicketSelect = useCallback(
    (ticketId: string) => {
      // CRITICAL FIX: Auto-expand the correct section when selecting a ticket
      const ticketSection = getTicketSection(ticketId);

      if (isCreatingTicket) {
        const draftData = localStorage.getItem('ticket-draft');
        if (draftData) {
          try {
            const parsedDraft = JSON.parse(draftData);

            // Only show confirmation if Title or Description have 5+ words (same as Create Ticket button)
            const titleWordCount = countWords(parsedDraft.title || '');
            const descriptionWordCount = countWords(
              parsedDraft.description || ''
            );

            if (titleWordCount >= 5 || descriptionWordCount >= 5) {
              setPendingTicketId(ticketId);
              setShowDraftConfirmation(true);
            } else {
              // Less than 5 words in both fields, proceed without confirmation
              localStorage.removeItem('ticket-draft');
              setIsCreatingTicket(false);
              setJustCreatedTicket(false);
              setSelectedTicketId(ticketId);
              setExpandSection(ticketSection);
            }
          } catch {
            // Invalid draft data, remove and proceed
            localStorage.removeItem('ticket-draft');
            setIsCreatingTicket(false);
            setJustCreatedTicket(false);
            setSelectedTicketId(ticketId);
            setExpandSection(ticketSection);
          }
        } else {
          setIsCreatingTicket(false);
          setJustCreatedTicket(false);
          setSelectedTicketId(ticketId);
          setExpandSection(ticketSection);
        }
      } else {
        // Check for reply draft in current ticket before navigating
        if (selectedTicketId && selectedTicketId !== ticketId) {
          const currentReplyDraft = loadDraft(selectedTicketId);
          if (currentReplyDraft && hasValidContent(currentReplyDraft)) {
            setPendingReplyNavigation(ticketId);
            setShowReplyDraftConfirmation(true);
            return;
          }
        }
        setJustCreatedTicket(false);
        setSelectedTicketId(ticketId);
        setExpandSection(ticketSection);
      }
    },
    [
      isCreatingTicket,
      setSelectedTicketId,
      selectedTicketId,
      loadDraft,
      hasValidContent,
      getTicketSection,
    ]
  );

  const handleSaveChanges = useCallback(() => {
    setShowDraftConfirmation(false);
    setIsCreatingTicket(false);
    if (pendingTicketId) {
      const ticketSection = getTicketSection(pendingTicketId);
      setSelectedTicketId(pendingTicketId);
      setExpandSection(ticketSection);
      setPendingTicketId(null);
    }
  }, [pendingTicketId, setSelectedTicketId, getTicketSection]);

  const handleDiscardChanges = useCallback(() => {
    localStorage.removeItem('ticket-draft');
    setShowDraftConfirmation(false);
    setIsCreatingTicket(false);
    if (pendingTicketId) {
      setSelectedTicketId(pendingTicketId);
      setPendingTicketId(null);
    }
  }, [pendingTicketId, setSelectedTicketId]);

  const handleDialogClose = useCallback(() => {
    setShowDraftConfirmation(false);
    setPendingTicketId(null);
  }, []);

  const handleSubmitTicket = useCallback(
    async (data: CreateTicketFormData & { attachment_ids: string[] }) => {
      if (!user || !tenantId) return;

      setIsSubmitting(true);

      // React Query mutation handles optimistic updates automatically

      // Use React Query mutation for optimistic updates and API call
      setIsCreatingTicket(false);
      setJustCreatedTicket(true);

      try {
        // Transform form data to match API schema (camelCase to snake_case)
        const apiData = {
          title: data.title,
          description: data.description,
          priority: data.priority,
          department: data.department,
          ...(data.assignedTo && { assigned_to: data.assignedTo }), // Only include if not empty
          cc: data.cc || [],
          attachment_ids: data.attachment_ids || [],
        };

        // DEBUG: Log the transformation
        console.log(
          '[DEBUG] Frontend form data:',
          JSON.stringify(data, null, 2)
        );
        console.log(
          '[DEBUG] Transformed API data:',
          JSON.stringify(apiData, null, 2)
        );

        // Use React Query mutation with optimistic updates
        const result = await createTicketMutation.mutateAsync(apiData);
        const realTicket = result.ticket as Ticket;

        // CRITICAL FIX: Selection transition is now handled by the mutation's onSuccess callback
        // This prevents unnecessary reloads and maintains smooth UX

        // Show success toast notification
        toast.success('Ticket Created Successfully', {
          description:
            'Your ticket has been created and assigned for processing.',
          duration: 4000,
        });
      } catch (error) {
        console.error('Error submitting ticket:', error);

        // IMPROVED: Properly handle optimistic update cleanup on error
        // The React Query mutation's onError handler will automatically rollback
        // the optimistic update, but we should also ensure UI state is consistent

        // Reset form state on error
        setIsCreatingTicket(false);
        setJustCreatedTicket(false);

        // Show detailed error toast notification
        const errorMessage =
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred';
        toast.error('Failed to Create Ticket', {
          description: `${errorMessage}. Please try again.`,
          duration: 6000,
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [user, tenantId, createTicketMutation, setSelectedTicketId]
  );

  // Reply draft handlers
  const handleReplyDraftSave = useCallback(() => {
    setShowReplyDraftConfirmation(false);
    if (pendingReplyNavigation) {
      setSelectedTicketId(pendingReplyNavigation);
      setPendingReplyNavigation(null);
    } else if (pendingCreateTicket) {
      setIsCreatingTicket(true);
      setJustCreatedTicket(false);
      setSelectedTicketId(null);
      setPendingCreateTicket(false);
    }
  }, [pendingReplyNavigation, pendingCreateTicket, setSelectedTicketId]);

  const handleReplyDraftDiscard = useCallback(() => {
    setShowReplyDraftConfirmation(false);
    // CRITICAL FIX: Clear the reply draft from localStorage when discarding
    if (selectedTicketId) {
      clearDraft(selectedTicketId);
    }
    if (pendingReplyNavigation) {
      setSelectedTicketId(pendingReplyNavigation);
      setPendingReplyNavigation(null);
    } else if (pendingCreateTicket) {
      setIsCreatingTicket(true);
      setJustCreatedTicket(false);
      setSelectedTicketId(null);
      setPendingCreateTicket(false);
    }
  }, [
    pendingReplyNavigation,
    pendingCreateTicket,
    setSelectedTicketId,
    selectedTicketId,
    clearDraft,
  ]);

  const handleReplyDraftCancel = useCallback(() => {
    setShowReplyDraftConfirmation(false);
    setPendingReplyNavigation(null);
    setPendingCreateTicket(false);
  }, []);

  return {
    selectedTicket,
    isCreatingTicket,
    isSubmitting,
    showDraftConfirmation,
    showReplyDraftConfirmation,
    handleCreateTicket,
    handleCancelCreateTicket,
    handleTicketSelect,
    handleSaveChanges,
    handleDiscardChanges,
    handleDialogClose,
    handleSubmitTicket,
    handleReplyDraftSave,
    handleReplyDraftDiscard,
    handleReplyDraftCancel,
    expandSection,
  };
}
