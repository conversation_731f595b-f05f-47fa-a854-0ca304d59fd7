/**
 * Intelligent Cache Manager Service - 2025 Optimized
 *
 * Provides surgical cache updates and intelligent ticket reordering
 * to minimize re-renders while maintaining data freshness and proper sorting.
 * Designed to work seamlessly with React Query and real-time updates.
 *
 * Key Features:
 * - Surgical cache updates to minimize re-renders
 * - Intelligent ticket reordering based on activity
 * - Performance monitoring and optimization
 * - Memory-efficient operations
 * - Conflict resolution for concurrent updates
 *
 * <AUTHOR> Augster
 * @version 1.0 - Intelligent Cache Management (January 2025)
 */

import { QueryClient } from '@tanstack/react-query';
import { Ticket } from '@/features/ticketing/models/ticket.schema';

// Cache update operation types
type CacheOperation = 'insert' | 'update' | 'delete' | 'reorder';

// Cache update context for tracking changes
interface CacheUpdateContext {
  operation: CacheOperation;
  ticketId: string;
  tenantId: string;
  timestamp: number;
  reason: string;
}

// Performance metrics for monitoring
interface CachePerformanceMetrics {
  totalUpdates: number;
  surgicalUpdates: number;
  fullReorders: number;
  averageUpdateTime: number;
  lastUpdateTime: number;
}

// Configuration for cache behavior
interface CacheConfig {
  enableSurgicalUpdates: boolean;
  enableIntelligentReordering: boolean;
  maxReorderFrequency: number; // Max reorders per minute
  performanceLogging: boolean;
}

const DEFAULT_CACHE_CONFIG: CacheConfig = {
  enableSurgicalUpdates: true,
  enableIntelligentReordering: true,
  maxReorderFrequency: 10, // Conservative limit
  performanceLogging: true,
};

export class IntelligentCacheManager {
  private static instance: IntelligentCacheManager;
  private config: CacheConfig;
  private metrics: CachePerformanceMetrics;
  private recentUpdates: CacheUpdateContext[];
  private reorderHistory: number[];

  private constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CACHE_CONFIG, ...config };
    this.metrics = {
      totalUpdates: 0,
      surgicalUpdates: 0,
      fullReorders: 0,
      averageUpdateTime: 0,
      lastUpdateTime: 0,
    };
    this.recentUpdates = [];
    this.reorderHistory = [];

    // Clean up old tracking data every minute
    setInterval(() => this.cleanupTrackingData(), 60000);
  }

  public static getInstance(
    config?: Partial<CacheConfig>
  ): IntelligentCacheManager {
    if (!IntelligentCacheManager.instance) {
      IntelligentCacheManager.instance = new IntelligentCacheManager(config);
    }
    return IntelligentCacheManager.instance;
  }

  /**
   * Intelligently update a single ticket in cache with minimal re-renders
   */
  public updateTicketInCache(
    queryClient: QueryClient,
    queryKey: string[],
    updatedTicket: Ticket,
    context: Omit<CacheUpdateContext, 'timestamp'> = {
      operation: 'update',
      ticketId: updatedTicket.id,
      tenantId: updatedTicket.tenantId,
      reason: 'real-time update',
    }
  ): boolean {
    const startTime = performance.now();

    try {
      const fullContext: CacheUpdateContext = {
        ...context,
        timestamp: Date.now(),
      };

      this.trackUpdate(fullContext);

      if (!this.config.enableSurgicalUpdates) {
        // Fallback to standard update
        return this.standardCacheUpdate(queryClient, queryKey, updatedTicket);
      }

      // Perform surgical update
      const success = queryClient.setQueryData(
        queryKey,
        (oldData: Ticket[] | undefined) => {
          if (!oldData) {
            return [updatedTicket];
          }

          const existingIndex = oldData.findIndex(
            (ticket) => ticket.id === updatedTicket.id
          );

          if (existingIndex === -1) {
            // Ticket doesn't exist - add it
            return this.insertTicketWithIntelligentOrdering(
              oldData,
              updatedTicket
            );
          }

          // Ticket exists - check if surgical update is beneficial
          const existingTicket = oldData[existingIndex];

          if (this.shouldUseSurgicalUpdate(existingTicket, updatedTicket)) {
            // Surgical update: only replace the specific ticket
            const newData = [...oldData];
            newData[existingIndex] = updatedTicket;

            // Check if reordering is needed
            if (this.shouldReorderAfterUpdate(existingTicket, updatedTicket)) {
              return this.reorderTicketsIntelligently(newData);
            }

            this.metrics.surgicalUpdates++;
            return newData;
          } else {
            // Full update needed
            return this.insertTicketWithIntelligentOrdering(
              oldData.filter((ticket) => ticket.id !== updatedTicket.id),
              updatedTicket
            );
          }
        }
      );

      const endTime = performance.now();
      this.updatePerformanceMetrics(endTime - startTime);

      if (this.config.performanceLogging) {
        console.log(
          `🎯 Cache Manager: ${fullContext.operation} for ticket ${fullContext.ticketId} (${(endTime - startTime).toFixed(2)}ms)`
        );
      }

      return success !== undefined;
    } catch (error) {
      console.error('Cache Manager: Error during intelligent update:', error);
      return false;
    }
  }

  /**
   * Insert ticket with intelligent ordering
   */
  private insertTicketWithIntelligentOrdering(
    existingTickets: Ticket[],
    newTicket: Ticket
  ): Ticket[] {
    const newData = [newTicket, ...existingTickets];
    return this.reorderTicketsIntelligently(newData);
  }

  /**
   * Intelligently reorder tickets based on activity and priority
   */
  private reorderTicketsIntelligently(tickets: Ticket[]): Ticket[] {
    if (!this.config.enableIntelligentReordering || !this.canReorder()) {
      return tickets;
    }

    this.trackReorder();
    this.metrics.fullReorders++;

    // Multi-criteria sorting: updated_at (primary), priority (secondary), created_at (tertiary)
    return tickets.sort((a, b) => {
      // Primary: Most recently updated first
      const aUpdated = new Date(a.updatedAt).getTime();
      const bUpdated = new Date(b.updatedAt).getTime();

      if (aUpdated !== bUpdated) {
        return bUpdated - aUpdated;
      }

      // Secondary: Higher priority first (urgent > high > medium > low)
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const aPriority = priorityOrder[a.priority] || 1;
      const bPriority = priorityOrder[b.priority] || 1;

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // Tertiary: Most recently created first
      const aCreated = new Date(a.createdAt).getTime();
      const bCreated = new Date(b.createdAt).getTime();

      return bCreated - aCreated;
    });
  }

  /**
   * Determine if surgical update should be used
   */
  private shouldUseSurgicalUpdate(
    existingTicket: Ticket,
    updatedTicket: Ticket
  ): boolean {
    // Use surgical update if only minor fields changed
    const significantFields = ['status', 'priority', 'assignedTo', 'title'];

    const hasSignificantChanges = significantFields.some((field) => {
      return (
        existingTicket[field as keyof Ticket] !==
        updatedTicket[field as keyof Ticket]
      );
    });

    // If no significant changes, use surgical update
    return !hasSignificantChanges;
  }

  /**
   * Determine if reordering is needed after update
   */
  private shouldReorderAfterUpdate(
    existingTicket: Ticket,
    updatedTicket: Ticket
  ): boolean {
    // Reorder if priority changed or if updated recently (within last 5 minutes)
    const priorityChanged = existingTicket.priority !== updatedTicket.priority;
    const recentlyUpdated =
      Date.now() - new Date(updatedTicket.updatedAt).getTime() < 300000; // 5 minutes

    return priorityChanged || recentlyUpdated;
  }

  /**
   * Check if reordering is allowed based on rate limiting
   */
  private canReorder(): boolean {
    const now = Date.now();
    const recentReorders = this.reorderHistory.filter(
      (timestamp) => now - timestamp < 60000
    );

    return recentReorders.length < this.config.maxReorderFrequency;
  }

  /**
   * Track reorder operation
   */
  private trackReorder(): void {
    this.reorderHistory.push(Date.now());
  }

  /**
   * Standard cache update fallback
   */
  private standardCacheUpdate(
    queryClient: QueryClient,
    queryKey: string[],
    updatedTicket: Ticket
  ): boolean {
    const success = queryClient.setQueryData(
      queryKey,
      (oldData: Ticket[] | undefined) => {
        if (!oldData) {
          return [updatedTicket];
        }

        const filteredData = oldData.filter(
          (ticket) => ticket.id !== updatedTicket.id
        );
        return this.reorderTicketsIntelligently([
          updatedTicket,
          ...filteredData,
        ]);
      }
    );

    return success !== undefined;
  }

  /**
   * Track cache update for analytics
   */
  private trackUpdate(context: CacheUpdateContext): void {
    this.recentUpdates.push(context);
    this.metrics.totalUpdates++;
  }

  /**
   * Update performance metrics
   */
  private updatePerformanceMetrics(updateTime: number): void {
    this.metrics.lastUpdateTime = updateTime;

    // Calculate rolling average
    const alpha = 0.1; // Smoothing factor
    this.metrics.averageUpdateTime =
      this.metrics.averageUpdateTime * (1 - alpha) + updateTime * alpha;
  }

  /**
   * Clean up old tracking data
   */
  private cleanupTrackingData(): void {
    const now = Date.now();

    // Keep only last hour of updates
    this.recentUpdates = this.recentUpdates.filter(
      (update) => now - update.timestamp < 3600000
    );

    // Keep only last hour of reorder history
    this.reorderHistory = this.reorderHistory.filter(
      (timestamp) => now - timestamp < 3600000
    );
  }

  /**
   * Get current performance metrics
   */
  public getPerformanceMetrics(): CachePerformanceMetrics & {
    recentUpdatesCount: number;
    reorderRate: number;
  } {
    const now = Date.now();
    const recentUpdatesCount = this.recentUpdates.filter(
      (update) => now - update.timestamp < 300000 // Last 5 minutes
    ).length;

    const recentReorders = this.reorderHistory.filter(
      (timestamp) => now - timestamp < 60000 // Last minute
    ).length;

    return {
      ...this.metrics,
      recentUpdatesCount,
      reorderRate: recentReorders,
    };
  }

  /**
   * Reset metrics (useful for testing)
   */
  public resetMetrics(): void {
    this.metrics = {
      totalUpdates: 0,
      surgicalUpdates: 0,
      fullReorders: 0,
      averageUpdateTime: 0,
      lastUpdateTime: 0,
    };
    this.recentUpdates = [];
    this.reorderHistory = [];
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🔧 Cache Manager: Configuration updated', this.config);
  }
}

// Export singleton instance
export const intelligentCacheManager = IntelligentCacheManager.getInstance();
