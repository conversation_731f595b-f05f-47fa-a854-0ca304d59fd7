'use client';

/**
 * TicketCard Component - Enhanced with React 19 Performance Optimizations
 *
 * Displays ticket information in a card format with safe HTML rendering
 * for ticket descriptions. Prevents XSS attacks while preserving formatting.
 *
 * Key Features:
 * - React.memo for preventing unnecessary re-renders
 * - useMemo for expensive computations (time formatting, initials)
 * - useCallback for event handlers
 * - Safe HTML rendering with DOMPurify sanitization
 * - Automatic plain text fallback for non-HTML content
 * - Truncated preview with proper HTML handling
 * - XSS protection for user-generated content
 *
 * <AUTHOR> Augster
 * @version 3.0 - React 19 Performance Optimized (January 2025)
 */

import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import { SafeHtmlPreview } from '@/features/shared/components/SafeHtml';
import { Badge } from '@/features/shared/components/ui/badge';
import { Button } from '@/features/shared/components/ui/button';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { Play } from 'lucide-react';
import { memo, useCallback, useMemo, useState } from 'react';
import {
  departmentConfig,
  priorityConfig,
  statusColors,
} from '../config/ticket-options';
import { useOpenTicket } from '@/hooks/useTickets';
import { Ticket } from '../models/ticket.schema';

interface TicketCardProps {
  ticket: Ticket & { isOptimistic?: boolean };
  isSelected?: boolean;
  onClick?: () => void;
  hideStatus?: boolean;
  showOpenButton?: boolean;
  onTicketOpened?: (ticketId: string) => void;
}

export const TicketCard = memo(function TicketCard({
  ticket,
  isSelected,
  onClick,
  hideStatus = false,
  showOpenButton = false,
  onTicketOpened,
}: TicketCardProps) {
  const { role, tenantId } = useAuth();
  const openTicketMutation = useOpenTicket(tenantId || '');

  const timeAgo = useMemo(() => {
    try {
      // Ensure we have a valid date
      const date =
        ticket.createdAt instanceof Date
          ? ticket.createdAt
          : new Date(ticket.createdAt);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }

      return formatDistanceToNow(date, { addSuffix: true })
        .replace('about ', '')
        .replace(' ago', '')
        .replace('minutes', 'mins')
        .replace('hours', 'hrs');
    } catch {
      return 'Invalid date';
    }
  }, [ticket.createdAt]);

  const handleClick = useCallback(() => {
    onClick?.();
  }, [onClick]);

  // Handle opening ticket
  const handleOpenTicket = useCallback(
    async (e: React.MouseEvent) => {
      e.stopPropagation(); // Prevent card selection

      if (openTicketMutation.isPending) return;

      try {
        await openTicketMutation.mutateAsync(ticket.id);
        onTicketOpened?.(ticket.id);
      } catch {
        // Error handling is done by the mutation
      }
    },
    [ticket.id, openTicketMutation, onTicketOpened]
  );

  // Determine if we should show the open button
  const shouldShowOpenButton =
    showOpenButton && ticket.status === 'new' && role === 'agent';

  return (
    <div
      className={cn(
        'p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer transition-all duration-300',
        !isSelected && 'hover:bg-gray-50 dark:hover:bg-gray-700',
        isSelected &&
          'bg-blue-50/60 dark:bg-blue-900/30 border-blue-200/50 dark:border-blue-600/50',
        ticket.isOptimistic && 'opacity-70'
      )}
      onClick={handleClick}
    >
      <div className='flex items-start gap-3'>
        {/* Avatar */}
        <ProfileAvatar
          avatarUrl={ticket.userAvatar ?? null}
          name={ticket.userName}
          email={ticket.userEmail}
          className='h-8 w-8 shrink-0 border border-blue-200 dark:border-blue-600'
          fallbackClassName='bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 text-xs font-medium'
        />

        {/* Content */}
        <div className='flex-1 min-w-0'>
          {/* Header */}
          <div className='flex items-center justify-between mb-1'>
            <h3 className='font-medium text-gray-900 dark:text-gray-100 text-sm truncate'>
              {ticket.userName}
            </h3>
            <span className='text-xs text-gray-500 dark:text-gray-400 shrink-0 ml-2'>
              {timeAgo}
            </span>
          </div>

          {/* Message preview - safely renders HTML content */}
          <SafeHtmlPreview
            content={ticket.description}
            maxLength={100}
            className='text-sm text-gray-600 dark:text-gray-300 mb-2 line-clamp-2'
            as='p'
          />

          {/* Badges */}
          <div className='flex flex-wrap gap-1'>
            {!hideStatus && ticket.status && (
              <Badge
                variant='secondary'
                className={cn('text-xs', statusColors[ticket.status])}
              >
                {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
              </Badge>
            )}

            {ticket.priority && (
              <Badge
                variant='secondary'
                className={cn('text-xs', priorityConfig[ticket.priority].color)}
              >
                {ticket.priority.charAt(0).toUpperCase() +
                  ticket.priority.slice(1)}{' '}
                Priority
              </Badge>
            )}

            {ticket.department && (
              <Badge
                variant='secondary'
                className={cn(
                  'text-xs',
                  departmentConfig[ticket.department].color
                )}
              >
                {ticket.department.charAt(0).toUpperCase() +
                  ticket.department.slice(1)}{' '}
                Department
              </Badge>
            )}

            {/* Auto-assignment indicator removed from sidebar - only show in detail view */}
          </div>

          {/* Open This Ticket Button */}
          {shouldShowOpenButton && (
            <div className='mt-3 pt-3 border-t border-gray-100 dark:border-gray-600'>
              <Button
                onClick={handleOpenTicket}
                disabled={openTicketMutation.isPending}
                size='sm'
                className='w-full bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700 disabled:opacity-50'
              >
                <Play className='h-3 w-3 mr-2' />
                {openTicketMutation.isPending
                  ? 'Opening...'
                  : 'Open This Ticket'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});
