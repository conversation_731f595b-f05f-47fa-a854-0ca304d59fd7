import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { SupabaseClient } from '@supabase/supabase-js';

async function getTenantUuid(
  serviceSupabase: SupabaseClient,
  tenantParam: string
) {
  if (
    !tenantParam.match(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    )
  ) {
    const { data: tenantData, error: tenantError } = await serviceSupabase
      .from('tenants')
      .select('id')
      .eq('subdomain', tenantParam)
      .single();
    if (tenantError || !tenantData) {
      throw new Error(`Tenant '${tenantParam}' not found`);
    }
    return tenantData.id;
  }
  return tenantParam;
}

async function validateUserAccess(
  serviceSupabase: SupabaseClient,
  userId: string,
  tenantUuid: string
) {
  const { data: userData, error: userError } = await serviceSupabase
    .from('users')
    .select('id, tenant_id, role, status')
    .eq('clerk_id', userId)
    .single();
  if (userError || !userData) {
    throw new Error('User not found');
  }
  if (userData.tenant_id !== tenantUuid) {
    throw new Error('Access denied to this tenant');
  }
  return userData;
}

/**
 * GET /api/user/database-id
 *
 * Returns the database UUID for the current authenticated user
 * Uses service client to bypass RLS issues with role resolution
 */
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Simplified tenant extraction from referer header
    const referer = request.headers.get('referer');
    let tenantSubdomain: string | null = null;

    if (referer) {
      try {
        const refererUrl = new URL(referer);
        const hostname = refererUrl.hostname;

        // Extract subdomain (e.g., quantumnest from quantumnest.localhost:3000)
        if (hostname.includes('.')) {
          tenantSubdomain = hostname.split('.')[0];
        }
      } catch (error) {
        console.error('Failed to parse referer URL:', error);
      }
    }

    // Fallback: try to extract from request URL
    if (!tenantSubdomain) {
      const url = new URL(request.url);
      const hostname = url.hostname;
      if (hostname.includes('.')) {
        tenantSubdomain = hostname.split('.')[0];
      }
    }

    if (!tenantSubdomain || tenantSubdomain === 'localhost') {
      return NextResponse.json(
        {
          error: 'Unable to determine tenant from request',
          debug: {
            referer,
            hostname: new URL(request.url).hostname,
          },
        },
        { status: 400 }
      );
    }

    const serviceSupabase = createServiceSupabaseClient();

    // Get tenant UUID and validate user access with tenant isolation
    const tenantUuid = await getTenantUuid(serviceSupabase, tenantSubdomain);
    const userData = await validateUserAccess(
      serviceSupabase,
      userId,
      tenantUuid
    );

    return NextResponse.json({
      databaseId: userData.id,
      clerkId: userId,
    });
  } catch (error) {
    console.error('Error in /api/user/database-id:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
