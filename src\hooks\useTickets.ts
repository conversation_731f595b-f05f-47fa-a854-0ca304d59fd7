/**
 * Custom React Query Hooks for Tickets - 2025 Optimized
 *
 * These hooks abstract all data-fetching logic and reduce component code by 90%
 * Following TkDodo's best practices for React Query v5
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ticketQueryOptions } from '@/lib/query-options';
import { QueryKeys } from '@/lib/query-keys';
import type {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useTenantUuid, useRealtimeQuery } from '@/hooks/useRealtimeQuery';
import { useSupabaseClient } from '@/lib/supabase-clerk';

interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
}

interface TicketFilterOptions {
  status?: string[];
  priority?: string[];
  roleFilter?: 'new' | 'assigned' | 'all';
}

interface CreateTicketData {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  department: 'sales' | 'support' | 'marketing' | 'technical';
  assigned_to?: string;
  assignedTo?: string; // Support both camelCase and snake_case
  assigned_to_clerk_id?: string;
  assignedToClerkId?: string; // Support both camelCase and snake_case
  cc?: string[];
  attachment_ids?: string[];
}

interface UpdateTicketData {
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  assigned_to?: string;
}

// Custom hook for ticket list - reduces component code by ~90%
export const useTickets = (
  context: RoleBasedFilterContext,
  options?: TicketFilterOptions & { enabled?: boolean }
) => {
  const { enabled = true, ...filterOptions } = options || {};
  return useQuery({
    ...ticketQueryOptions.list(context, filterOptions),
    enabled,
  });
};

// Custom hook for ticket detail with select optimization
export const useTicket = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  return useQuery({
    ...ticketQueryOptions.detail(tenantId, ticketId),
    enabled: enabled && !!tenantId && !!ticketId && !isOptimisticTicket,
  });
};

// Real-time version of useTicket for ticket detail page
export const useRealtimeTicket = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  const { supabase } = useSupabaseClient();

  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  // Resolve tenant UUID first
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  const realtimeQuery = useRealtimeQuery<Ticket>(
    [...QueryKeys.TICKETS.detail(tenantId, ticketId)],
    async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      console.log(
        '🔍 Fetching ticket detail for ticket:',
        ticketId,
        'tenant UUID:',
        tenantUuid
      );

      const response = await fetch(
        `/api/tickets/${ticketId}?tenant_id=${tenantUuid}`
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch ticket');
      }

      const data = await response.json();
      return [data.data]; // Return as array for useRealtimeQuery
    },
    'tickets',
    {
      filter: `id=eq.${ticketId}`,
      queryOptions: {
        enabled:
          enabled &&
          !!tenantId &&
          !!ticketId &&
          !isOptimisticTicket &&
          !!tenantUuid,
      },
    }
  );

  // Transform the array result to single ticket to match useTicket interface
  return {
    ...realtimeQuery,
    data: realtimeQuery.data?.[0] || undefined,
  };
};

// Custom hook for ticket messages with real-time updates
export const useTicketMessages = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  // CRITICAL FIX: Resolve tenant UUID for proper real-time subscription
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Use real-time query for messages to enable cross-user updates
  return useRealtimeQuery<TicketMessage>(
    [...QueryKeys.TICKETS.messages(tenantUuid || tenantId, ticketId)],
    async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      console.log(
        '🔍 Fetching messages for ticket:',
        ticketId,
        'tenant UUID:',
        tenantUuid
      );

      const response = await fetch(
        `/api/tickets/${ticketId}/messages?tenant_id=${tenantUuid}`
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch messages');
      }

      const data = await response.json();
      return data.messages || [];
    },
    'ticket_messages',
    {
      queryOptions: {
        enabled:
          enabled &&
          !!tenantId &&
          !!ticketId &&
          !isOptimisticTicket &&
          !!tenantUuid,
        staleTime: 1000 * 60 * 5, // 5 minutes
        gcTime: 1000 * 60 * 10, // 10 minutes
      },
      filter: `ticket_id=eq.${ticketId}`,
      schema: 'public',
    }
  );
};

// Custom mutation hook for opening tickets with optimistic updates
export const useOpenTicket = (tenantId: string) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async (ticketId: string) => {
      // Get tenant ID from the current subdomain
      const hostname = window.location.hostname;
      const parts = hostname.split('.');
      const currentTenantId = parts.length > 1 ? parts[0] : null;

      if (
        !currentTenantId ||
        currentTenantId === 'localhost' ||
        currentTenantId === 'www'
      ) {
        throw new Error('Tenant ID not available');
      }

      const response = await fetch(`/api/tickets/${ticketId}/open`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenant_id: currentTenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return response.json();
    },
    onMutate: async (ticketId: string) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
      });

      // CRITICAL FIX: Snapshot previous value (could be single ticket or array)
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId)
      );

      // CRITICAL FIX: Optimistically update the ticket status to 'open' as array to match useRealtimeQuery format
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId),
        (old: Ticket | Ticket[] | undefined) => {
          // Handle both single ticket and array formats for compatibility
          const currentTicket = Array.isArray(old) ? old[0] : old;
          if (!currentTicket) return old;

          // CRITICAL FIX: Preserve all existing metadata, especially assignment information
          const existingMetadata =
            (currentTicket.metadata as Record<string, unknown>) || {};

          const updatedTicket = {
            ...currentTicket,
            status: 'open' as const,
            updatedAt: new Date(),
            metadata: {
              ...existingMetadata,
              // Add opening metadata without overwriting assignment data
              opening: {
                opened_by: user?.id,
                opened_at: new Date().toISOString(),
                opened_by_role: user?.publicMetadata?.role || 'agent',
              },
            },
          };

          // Always return as array to match useRealtimeQuery expectations
          return [updatedTicket];
        }
      );

      // Also update the ticket in all list queries - PRESERVE ASSIGNMENT METADATA
      queryClient.setQueriesData(
        {
          queryKey: ['tickets', tenantId, 'list'],
          exact: false,
        },
        (old: Ticket[] | undefined) => {
          if (!old) return old;
          return old.map((ticket) =>
            ticket.id === ticketId
              ? {
                  ...ticket,
                  status: 'open' as const,
                  updatedAt: new Date(),
                  // CRITICAL FIX: Preserve existing metadata including assignment information
                  metadata: {
                    ...((ticket.metadata as Record<string, unknown>) || {}),
                    opening: {
                      opened_by: user?.id,
                      opened_at: new Date().toISOString(),
                      opened_by_role: user?.publicMetadata?.role || 'agent',
                    },
                  },
                }
              : ticket
          );
        }
      );

      // Update realtime queries for immediate UI feedback - PRESERVE ASSIGNMENT METADATA
      queryClient.setQueriesData(
        {
          queryKey: ['realtime-tickets', tenantId],
          exact: false,
        },
        (old: Ticket[] | undefined) => {
          if (!old) return old;
          return old.map((ticket) =>
            ticket.id === ticketId
              ? {
                  ...ticket,
                  status: 'open' as const,
                  updatedAt: new Date(),
                  // CRITICAL FIX: Preserve existing metadata including assignment information
                  metadata: {
                    ...((ticket.metadata as Record<string, unknown>) || {}),
                    opening: {
                      opened_by: user?.id,
                      opened_at: new Date().toISOString(),
                      opened_by_role: user?.publicMetadata?.role || 'agent',
                    },
                  },
                }
              : ticket
          );
        }
      );

      return { previousTicket };
    },
    onError: (_err, ticketId, context) => {
      // CRITICAL FIX: Rollback on error with proper array format
      if (context?.previousTicket) {
        // Ensure rollback data is in array format to match useRealtimeQuery expectations
        const rollbackData = Array.isArray(context.previousTicket)
          ? context.previousTicket
          : [context.previousTicket];
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(tenantId, ticketId),
          rollbackData
        );
      }
    },
    onSettled: (_data, error, ticketId) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic ticket opening should remain stable without refetching
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
      // On success, the optimistic update is already in place and should remain
    },
  });
};

// Custom mutation hook with optimistic updates
export const useCreateTicket = (
  tenantId: string,
  onOptimisticTicketCreated?: (ticketId: string) => void,
  onRealTicketCreated?: (
    realTicketId: string,
    optimisticTicketId: string
  ) => void
) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async (ticketData: CreateTicketData) => {
      const response = await fetch('/api/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...ticketData,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create ticket');
      }

      return response.json();
    },
    onMutate: async (newTicket) => {
      // CRITICAL FIX: Use tenant UUID for proper query key matching
      const currentTenantUuid = tenantUuid || tenantId;

      // Cancel outgoing refetches for all ticket list queries
      await queryClient.cancelQueries({
        queryKey: ['tickets', currentTenantUuid, 'list'],
        exact: false,
      });

      // Snapshot previous values for all ticket list queries
      const previousTicketsData = new Map();
      queryClient
        .getQueriesData({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        })
        .forEach(([queryKey, data]) => {
          previousTicketsData.set(queryKey, data);
        });

      // Create optimistic ticket with unique ID and proper assignment data
      // Use crypto.randomUUID for stable client-side ID generation
      const optimisticId =
        typeof window !== 'undefined' && window.crypto?.randomUUID
          ? `optimistic-${window.crypto.randomUUID()}`
          : `optimistic-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const optimisticTicket = {
        ...newTicket,
        id: optimisticId,
        tenantId: currentTenantUuid,
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: user?.id || 'temp-user',
        userName: user
          ? `${user.firstName || ''} ${user.lastName || ''}`.trim() ||
            'Unknown User'
          : 'Creating...',
        userEmail: user?.primaryEmailAddress?.emailAddress || '',
        messages: [],
        attachments: [],
        status: 'new',
        tags: [],
        metadata: {},
        // CRITICAL FIX: Include assignment data in optimistic update to prevent timing issues
        // Handle both camelCase (frontend) and snake_case (API) field names
        assignedTo: newTicket.assignedTo || newTicket.assigned_to || undefined,
        assignedToClerkId:
          newTicket.assignedToClerkId ||
          newTicket.assigned_to_clerk_id ||
          undefined,
        // Additional assignment tracking fields for consistency
        assignedBy: undefined,
        assignedByClerkId: undefined,
        assignedAt:
          newTicket.assignedTo || newTicket.assigned_to
            ? new Date()
            : undefined,
      } as Ticket;

      // CRITICAL FIX: Update all ticket list queries with correct tenant UUID
      // This matches the query key pattern used by useRealtimeTickets
      queryClient.setQueriesData(
        {
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        },
        (old: Ticket[] | undefined) => {
          if (!old) return [optimisticTicket];
          return [optimisticTicket, ...old];
        }
      );

      // CRITICAL FIX: Immediately select the optimistic ticket for instant UI feedback
      if (onOptimisticTicketCreated) {
        onOptimisticTicketCreated(optimisticTicket.id);
      }

      return { previousTicketsData, optimisticTicket };
    },
    onSuccess: (result, _variables, context) => {
      // CRITICAL FIX: Replace optimistic ticket with real ticket data in cache
      // This maintains the ticket position in the list while updating with real data
      const realTicket = result.ticket as Ticket;
      const optimisticTicket = context?.optimisticTicket;

      if (optimisticTicket && realTicket) {
        const currentTenantUuid = tenantUuid || tenantId;

        // Update all ticket list queries to replace optimistic ticket with real ticket
        queryClient.setQueriesData(
          {
            queryKey: ['tickets', currentTenantUuid, 'list'],
            exact: false,
          },
          (old: Ticket[] | undefined) => {
            if (!old) return old;

            // Replace optimistic ticket with real ticket, maintaining position
            return old.map((ticket) =>
              ticket.id === optimisticTicket.id ? realTicket : ticket
            );
          }
        );

        // CRITICAL FIX: Set the real ticket in the detail cache as an array to match useRealtimeQuery format
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(currentTenantUuid, realTicket.id),
          [realTicket] // Store as array to match useRealtimeQuery expectations
        );

        // CRITICAL FIX: Initialize empty messages cache for the real ticket
        // This prevents unnecessary loading states when transitioning from optimistic ticket
        queryClient.setQueryData(
          QueryKeys.TICKETS.messages(currentTenantUuid, realTicket.id),
          []
        );

        // CRITICAL FIX: Notify about the transition from optimistic to real ticket
        if (onRealTicketCreated) {
          onRealTicketCreated(realTicket.id, optimisticTicket.id);
        }
      }
    },
    onError: (_err, _newTicket, context) => {
      // Rollback on error - restore all previous query data
      if (context?.previousTicketsData) {
        context.previousTicketsData.forEach((data, queryKey) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    onSettled: (_data, error) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic ticket creation should remain stable without refetching
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
      // On success, the optimistic ticket is already in place and should remain
    },
  });
};

// Custom mutation hook for updating tickets
export const useUpdateTicket = (tenantId: string) => {
  const queryClient = useQueryClient();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async ({
      ticketId,
      updates,
    }: {
      ticketId: string;
      updates: UpdateTicketData;
    }) => {
      const response = await fetch(`/api/tickets/${ticketId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update ticket');
      }

      return response.json();
    },
    onMutate: async ({ ticketId, updates }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
      });

      // Snapshot previous value
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId)
      );

      // Optimistically update cache
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId),
        (old: Ticket) =>
          old ? { ...old, ...updates, updatedAt: new Date() } : old
      );

      return { previousTicket };
    },
    onError: (_err, { ticketId }, context) => {
      // Rollback on error
      if (context?.previousTicket) {
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(tenantId, ticketId),
          context.previousTicket
        );
      }
    },
    onSettled: (_data, error, { ticketId }) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic updates should remain stable without refetching
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
      // On success, the optimistic update is already in place and should remain
    },
  });
};

// Custom hook for adding messages to tickets with optimistic updates
export const useAddTicketMessage = (tenantId: string, ticketId: string) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async (messageData: {
      content: string;
      attachment_ids?: string[];
      attachments?: Array<{
        id: string;
        name: string;
        type: string;
        size: number;
      }>;
    }) => {
      const response = await fetch(`/api/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...messageData,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to add message');
      }

      return response.json();
    },
    onMutate: async (messageData) => {
      // CRITICAL FIX: Use tenant UUID for proper query key matching
      const currentTenantUuid = tenantUuid || tenantId;

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
      });

      // Snapshot previous value
      const previousMessages = queryClient.getQueryData(
        QueryKeys.TICKETS.messages(currentTenantUuid, ticketId)
      );

      // Create optimistic message with attachments
      const optimisticAttachments = (messageData.attachments || []).map(
        (att) => ({
          id: att.id,
          name: att.name,
          type: att.type,
          size: att.size,
          url: `/api/attachments/${att.id}`,
          uploadedAt: new Date(),
        })
      );

      const optimisticMessage = {
        id: `optimistic-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        content: messageData.content,
        authorId: user?.id || '',
        authorName:
          `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || 'You',
        authorAvatar: user?.imageUrl || undefined,
        createdAt: new Date(),
        attachments: optimisticAttachments,
        ticketId: ticketId,
      };

      // Optimistically update messages cache
      queryClient.setQueryData(
        QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
        (old: TicketMessage[] | undefined) => {
          if (!old) return [optimisticMessage];
          return [...old, optimisticMessage];
        }
      );

      return { previousMessages };
    },
    onError: (_err, _messageData, context) => {
      // Rollback on error
      if (context?.previousMessages) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.setQueryData(
          QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
          context.previousMessages
        );
      }
    },
    onSettled: (_data, error) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic message updates should remain stable without refetching
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
        });
        // Also invalidate ticket detail to update message count on error
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });
      }
      // On success, the optimistic message is already in place and should remain
    },
  });
};
