'use client';

import { QueryClient } from '@tanstack/react-query';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { useState } from 'react';
import { ModernMessageCache } from '@/lib/cache/modern-dexie-cache';

// IndexedDB persister for optimal 2025 pattern
const createPersister = () => {
  return createSyncStoragePersister({
    storage: {
      getItem: (key: string) => {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      },
      setItem: (key: string, value: unknown) => {
        localStorage.setItem(key, JSON.stringify(value));
      },
      removeItem: (key: string) => {
        localStorage.removeItem(key);
      },
    },
    key: 'TicketingApp_ReactQueryCache',
    throttleTime: 1000,
  });
};

// Cleanup old databases
const cleanupOldDatabases = async () => {
  try {
    const databases = await indexedDB.databases();
    for (const db of databases) {
      if (db.name === 'TicketingCacheDB') {
        indexedDB.deleteDatabase(db.name);
        console.log('🗑️ Removed old database:', db.name);
      }
    }
  } catch (error) {
    console.warn('Failed to cleanup old databases:', error);
  }
};

// Global cache cleanup utility for logout only
export const clearAllCaches = async () => {
  try {
    localStorage.removeItem('TicketingApp_ReactQueryCache');
    await ModernMessageCache.cleanupExpiredCache();
    console.log('🧹 Cleared all caches on logout');
  } catch (error) {
    console.warn('Failed to clear caches:', error);
  }
};

// Optimal 2025 React Query Provider with PersistQueryClientProvider
function ReactQueryProviderInner({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 30 * 60 * 1000, // 30 minutes as per requirements
            gcTime: 60 * 60 * 1000, // 1 hour
            refetchOnWindowFocus: true,
            refetchOnMount: true,
            retry: (
              failureCount,
              error: Error & { status?: number; code?: number }
            ) => {
              if (error?.status === 401 || error?.code === 401) {
                return false;
              }
              return failureCount < 2;
            },
            retryDelay: (attemptIndex) =>
              Math.min(500 * 2 ** attemptIndex, 5000),
            refetchOnReconnect: true,
            networkMode: 'online',
          },
          mutations: {
            retry: (
              failureCount,
              error: Error & { status?: number; code?: number }
            ) => {
              if (error?.status === 401 || error?.code === 401) {
                return false;
              }
              return failureCount < 1;
            },
            networkMode: 'online',
          },
        },
      })
  );

  const persister = createPersister();

  // Cleanup old databases on mount
  if (typeof window !== 'undefined') {
    cleanupOldDatabases();
  }

  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister,
        maxAge: 30 * 60 * 1000, // 30 minutes as per requirements
        dehydrateOptions: {
          shouldDehydrateQuery: (query) => query.state.status === 'success',
        },
      }}
    >
      {children}
    </PersistQueryClientProvider>
  );
}

// Main component that gets tenant context
export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ReactQueryProviderInner>{children}</ReactQueryProviderInner>;
}
