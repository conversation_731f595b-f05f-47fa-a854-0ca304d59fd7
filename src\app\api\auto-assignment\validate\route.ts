import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { z } from 'zod';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { AutoAssignmentService } from '@/features/settings/services/auto-assignment.service';

const ValidateAutoAssignmentSchema = z.object({
  tenant_id: z.string().min(1),
  department: z.enum(['sales', 'support', 'marketing', 'technical']),
});

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const requestBody = await request.json();
    const validationResult =
      ValidateAutoAssignmentSchema.safeParse(requestBody);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { tenant_id, department } = validationResult.data;
    const serviceSupabase = createServiceSupabaseClient();

    // Get tenant UUID from tenant_id (which could be subdomain or UUID)
    let tenantUuid = tenant_id;

    // If tenant_id is not a UUID, treat it as subdomain and look up the UUID
    if (
      !tenant_id.match(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
      )
    ) {
      const { data: tenantData, error: tenantError } = await serviceSupabase
        .from('tenants')
        .select('id')
        .eq('subdomain', tenant_id)
        .single();

      if (tenantError || !tenantData) {
        return NextResponse.json(
          { error: 'Tenant not found' },
          { status: 404 }
        );
      }

      tenantUuid = tenantData.id;
    }

    // Check if auto-assignment is available for this department
    const autoAssignmentService = new AutoAssignmentService(serviceSupabase);
    const assignmentResult = await autoAssignmentService.getAssignmentForTicket(
      tenantUuid,
      department
    );

    const canAutoAssign = assignmentResult.assigned_agent_id !== null;

    return NextResponse.json({
      canAutoAssign,
      assignmentReason: assignmentResult.assignment_reason,
    });
  } catch (error) {
    console.error('Auto-assignment validation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
