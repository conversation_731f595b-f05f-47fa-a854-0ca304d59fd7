'use client';

import { useState, useEffect, useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import { useAuth } from '@clerk/nextjs';
import { Button } from '@/features/shared/components/ui/button';
import { Input } from '@/features/shared/components/ui/input';
import { useRealtimeTest, type TestEntry } from '@/hooks/useRealtimeQuery';

export default function RealtimeTestPage() {
  const { supabase } = useSupabaseClient();
  const { getToken } = useAuth();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [jwtToken, setJwtToken] = useState<string>('');

  // Get JWT token for debugging
  useEffect(() => {
    const fetchToken = async () => {
      try {
        const token = await getToken({ template: 'supabase' });
        setJwtToken(token || '');
      } catch (error) {
        console.error('Error getting JWT token:', error);
      }
    };
    fetchToken();
  }, [getToken]);

  // 🔥 NEW: Use the modern useRealtimeTest hook (2025 pattern)
  const { data: entries = [], isLoading } = useRealtimeTest(
    '39a83e10-5053-43e6-91a3-ba726ebc450a'
  );

  // Create entry mutation
  const createEntryMutation = useMutation({
    mutationFn: async (newEntry: { title: string; description: string }) => {
      console.log('🚀 Creating new entry:', newEntry);
      // Use type assertion to bypass TypeScript schema inference issues
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase as any)
        .from('realtime_test')
        .insert({
          title: newEntry.title,
          description: newEntry.description,
          tenant_id: '39a83e10-5053-43e6-91a3-ba726ebc450a', // quantumnest tenant
        })
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating entry:', error);
        throw error;
      }

      console.log('✅ Created entry:', data);
      return data as TestEntry;
    },
    onSuccess: () => {
      console.log('🎉 Entry created successfully');
      setTitle('');
      setDescription('');
    },
  });

  // 🔥 Real-time subscription is now handled automatically by useRealtimeTest hook!

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (title.trim() && description.trim()) {
        createEntryMutation.mutate({
          title: title.trim(),
          description: description.trim(),
        });
      }
    },
    [title, description, createEntryMutation]
  );

  return (
    <div className='container mx-auto p-8 max-w-4xl'>
      <h1 className='text-3xl font-bold mb-8'>Real-time Functionality Test</h1>

      {/* JWT Token Display */}
      <div className='mb-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg'>
        <h2 className='text-lg font-semibold mb-2'>
          JWT Token (for debugging):
        </h2>
        <div className='text-sm font-mono break-all bg-white dark:bg-gray-900 p-2 rounded border'>
          {jwtToken || 'Loading...'}
        </div>
        <p className='text-sm text-gray-600 dark:text-gray-400 mt-2'>
          You can test this token at{' '}
          <a
            href='https://jwt.io'
            target='_blank'
            rel='noopener noreferrer'
            className='text-blue-500 hover:underline'
          >
            jwt.io
          </a>
        </p>
      </div>

      {/* Create Entry Form */}
      <div className='mb-8 p-6 border rounded-lg'>
        <h2 className='text-xl font-semibold mb-4'>Create Test Entry</h2>
        <form onSubmit={handleSubmit} className='space-y-4'>
          <div>
            <label htmlFor='title' className='block text-sm font-medium mb-1'>
              Title
            </label>
            <Input
              id='title'
              type='text'
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder='Enter title...'
              required
            />
          </div>
          <div>
            <label
              htmlFor='description'
              className='block text-sm font-medium mb-1'
            >
              Description
            </label>
            <Input
              id='description'
              type='text'
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder='Enter description...'
              required
            />
          </div>
          <Button
            type='submit'
            disabled={
              createEntryMutation.isPending ||
              !title.trim() ||
              !description.trim()
            }
          >
            {createEntryMutation.isPending ? 'Creating...' : 'Create Entry'}
          </Button>
        </form>
      </div>

      {/* Entries List */}
      <div className='p-6 border rounded-lg'>
        <h2 className='text-xl font-semibold mb-4'>
          Test Entries ({entries.length})
        </h2>

        {isLoading ? (
          <p>Loading entries...</p>
        ) : entries.length === 0 ? (
          <p className='text-gray-500'>
            No entries yet. Create one above to test real-time functionality!
          </p>
        ) : (
          <div className='space-y-4'>
            {entries.map((entry) => (
              <div
                key={entry.id}
                className='p-4 border rounded bg-gray-50 dark:bg-gray-800'
              >
                <h3 className='font-semibold'>{entry.title}</h3>
                <p className='text-gray-600 dark:text-gray-400'>
                  {entry.description}
                </p>
                <p className='text-xs text-gray-500 mt-2'>
                  Created:{' '}
                  {entry.created_at
                    ? new Date(entry.created_at).toLocaleString()
                    : 'Unknown'}
                </p>
                <p className='text-xs text-gray-500'>ID: {entry.id}</p>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className='mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
        <h3 className='font-semibold mb-2'>
          🔥 Modern 2025 useRealtimeQuery() Pattern Testing:
        </h3>
        <ol className='list-decimal list-inside space-y-1 text-sm'>
          <li>
            This page now uses the modern{' '}
            <code className='bg-gray-200 dark:bg-gray-700 px-1 rounded'>
              useRealtimeQuery()
            </code>{' '}
            hook
          </li>
          <li>Open this page in two browser windows/tabs</li>
          <li>Create an entry in one window using the form above</li>
          <li>
            Check if the entry appears instantly in the other window without
            refresh
          </li>
          <li>Check the browser console for real-time event logs</li>
          <li>
            Use Supabase MCP to insert data directly and test real-time updates
          </li>
          <li>
            Notice how much cleaner the code is compared to manual
            subscriptions!
          </li>
        </ol>
        <div className='mt-3 p-2 bg-green-100 dark:bg-green-900/20 rounded text-sm'>
          <strong>✅ Benefits of useRealtimeQuery():</strong>
          <ul className='list-disc list-inside mt-1 space-y-1'>
            <li>Automatic subscription management</li>
            <li>Direct cache updates (no invalidation needed)</li>
            <li>Cleaner, more maintainable code</li>
            <li>Built-in error handling and cleanup</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
