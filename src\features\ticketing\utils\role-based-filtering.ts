/**
 * Role-based ticket filtering utilities
 * @version 1.0 - React 19 Enterprise Implementation (January 2025)
 */

import { Ticket } from '../models/ticket.schema';
import {
  RoleBasedFilterContext,
  TicketFilterParams,
  TicketOwnership,
  RoleBasedFilterFunction,
  FilteredTicketsResult,
  RoleDashboardConfig,
  TicketVisibilityRules,
} from '../types/role-based-filtering';

/**
 * Determines ticket ownership and access rights for a user
 * CRITICAL FIX: Now uses database UUIDs for consistent comparison
 */
export function determineTicketOwnership(
  ticket: Ticket,
  context: RoleBasedFilterContext & { clerkId?: string }
): TicketOwnership {
  // IMPROVED FIX: Handle both database UUID and Clerk ID matching for optimistic tickets
  const isCreator =
    ticket.userId === context.userId ||
    (context.clerkId && ticket.creatorClerkId === context.clerkId);
  const isAssigned = ticket.assignedTo === context.userId;
  const isAssigner = ticket.assignedBy === context.userId;

  let canAccess = false;
  let accessReason: TicketOwnership['accessReason'] = 'none';

  // Role-based access logic
  switch (context.role) {
    case 'super_admin':
      canAccess = true;
      accessReason = 'super_admin';
      break;
    case 'admin': {
      // Admin can see tickets they created/assigned/are assigned to, plus user-created tickets
      const isUserCreatedTicket =
        (ticket.metadata?.creator_role || 'user') === 'user';
      canAccess = isCreator || isAssigner || isAssigned || isUserCreatedTicket;
      if (isCreator) accessReason = 'creator';
      else if (isAssigner) accessReason = 'assigner';
      else if (isAssigned) accessReason = 'assigned';
      else if (isUserCreatedTicket) accessReason = 'admin';
      else accessReason = 'admin';
      break;
    }
    case 'agent':
      canAccess = !!isAssigned || !!isCreator;
      if (isAssigned) accessReason = 'assigned';
      else if (isCreator) accessReason = 'creator';
      break;
    case 'user':
      canAccess = !!isCreator;
      if (isCreator) accessReason = 'creator';
      break;
  }

  return {
    isCreator: !!isCreator,
    isAssigned: !!isAssigned,
    isAssigner: !!isAssigner,
    canAccess,
    accessReason,
  };
}

/**
 * Gets ticket visibility rules based on user role and ownership
 */
export function getTicketVisibilityRules(
  ticket: Ticket,
  context: RoleBasedFilterContext
): TicketVisibilityRules {
  const ownership = determineTicketOwnership(ticket, context);

  const rules: TicketVisibilityRules = {
    canView: ownership.canAccess,
    canEdit: false,
    canAssign: false,
    canReply: false,
    canClose: false,
  };

  if (!ownership.canAccess) {
    rules.reason = 'No access to this ticket';
    return rules;
  }

  // Role-based permissions
  switch (context.role) {
    case 'super_admin':
      rules.canEdit = true;
      rules.canAssign = true;
      rules.canReply = true;
      rules.canClose = true;
      break;
    case 'admin':
      rules.canEdit = ownership.isCreator || ownership.isAssigner;
      rules.canAssign = true;
      rules.canReply = ownership.isCreator || ownership.isAssigner;
      rules.canClose = ownership.isCreator || ownership.isAssigner;
      break;
    case 'agent':
      rules.canEdit = ownership.isAssigned;
      rules.canAssign = false;
      rules.canReply = ownership.isAssigned;
      rules.canClose = ownership.isAssigned;
      break;
    case 'user':
      rules.canEdit = ownership.isCreator && ticket.status === 'open';
      rules.canAssign = false;
      rules.canReply = ownership.isCreator;
      rules.canClose = false;
      break;
  }

  return rules;
}

/**
 * New Tickets filtering functions for role-based "New Tickets" section logic
 */

/**
 * Modern, simplified new tickets filters using functional composition
 */
const isCreatedByUser = (ticket: Ticket) =>
  (ticket.metadata?.creator_role || 'user') === 'user';
const isNewStatus = (ticket: Ticket) => ticket.status === 'new';
const isNewOrOpen = (ticket: Ticket) => ['new', 'open'].includes(ticket.status);

/**
 * Unified new tickets filter factory
 */
export const createNewTicketsFilter =
  (role: 'admin' | 'agent', userId?: string): RoleBasedFilterFunction =>
  (tickets, context) => {
    const filters = {
      // CRITICAL FIX: Admin sees all user-created tickets regardless of assignment status
      admin: [isCreatedByUser, isNewOrOpen],
      agent: [isAssignedTo(userId || context.userId), isNewStatus],
    }[role];

    return tickets.filter((ticket) =>
      filters.every((filter) => filter(ticket))
    );
  };

// Export role-specific filters for backward compatibility
export const filterNewTicketsForAdmins: RoleBasedFilterFunction = (
  tickets,
  context
) => createNewTicketsFilter('admin')(tickets, context);

export const filterNewTicketsForAgent: RoleBasedFilterFunction = (
  tickets,
  context
) => createNewTicketsFilter('agent')(tickets, context);

// CRITICAL FIX: Super admin sees only UNASSIGNED tickets with "new" status
export const filterNewTicketsForSuperAdmin: RoleBasedFilterFunction = (
  tickets
) => {
  return tickets.filter(
    (ticket) => ticket.status === 'new' && !isAssigned(ticket)
  );
};

/**
 * Modern, simplified assigned tickets filter using functional composition
 */
const ASSIGNED_STATUSES = ['new', 'open', 'pending'] as const;
const ACTIVE_STATUSES = ['open', 'pending'] as const;

const isAssigned = (ticket: Ticket) =>
  !!(ticket.assignedTo || ticket.assignedToClerkId);
const hasStatus = (statuses: readonly string[]) => (ticket: Ticket) =>
  statuses.includes(ticket.status);
const isAssignedBy = (userId: string) => (ticket: Ticket) =>
  ticket.assignedByClerkId === userId || ticket.assignedBy === userId;
const isAssignedTo = (userId: string) => (ticket: Ticket) =>
  ticket.assignedToClerkId === userId || ticket.assignedTo === userId;

/**
 * Unified assigned tickets filter - handles all roles with simple configuration
 */
export const createAssignedTicketsFilter =
  (
    role: 'super_admin' | 'admin' | 'agent',
    userId: string
  ): RoleBasedFilterFunction =>
  (tickets) => {
    const filters = {
      super_admin: [isAssigned, hasStatus(ASSIGNED_STATUSES)],
      admin: [isAssigned, isAssignedBy(userId), hasStatus(ASSIGNED_STATUSES)],
      agent: [isAssignedTo(userId), hasStatus(ACTIVE_STATUSES)],
    }[role];

    return tickets.filter((ticket) =>
      filters.every((filter) => filter(ticket))
    );
  };

// Export role-specific filters for backward compatibility
export const filterAssignedTicketsForSuperAdmin: RoleBasedFilterFunction = (
  tickets,
  context
) =>
  createAssignedTicketsFilter('super_admin', context.userId)(tickets, context);

export const filterAssignedTicketsForAdmin: RoleBasedFilterFunction = (
  tickets,
  context
) => createAssignedTicketsFilter('admin', context.userId)(tickets, context);

export const filterAssignedTicketsForAgent: RoleBasedFilterFunction = (
  tickets,
  context
) => createAssignedTicketsFilter('agent', context.userId)(tickets, context);

/**
 * Main role-based filtering function
 */
export function filterTicketsByRole(
  tickets: Ticket[],
  context: RoleBasedFilterContext,
  params?: Partial<TicketFilterParams>
): FilteredTicketsResult {
  let filteredTickets = tickets.filter((ticket) => {
    const ownership = determineTicketOwnership(ticket, context);
    return ownership.canAccess;
  });

  // Apply additional filters if provided
  if (params?.status && params.status.length > 0) {
    filteredTickets = filteredTickets.filter((ticket) =>
      params.status!.includes(ticket.status)
    );
  }

  if (params?.assignedOnly) {
    filteredTickets = filteredTickets.filter((ticket) => ticket.assignedTo);
  }

  if (params?.createdOnly) {
    filteredTickets = filteredTickets.filter(
      (ticket) => ticket.userId === context.userId
    );
  }

  return {
    tickets: filteredTickets,
    totalCount: tickets.length,
    filteredCount: filteredTickets.length,
    filterContext: context,
    appliedFilters: {
      role: context.role,
      userId: context.userId,
      tenantId: context.tenantId,
      ...params,
    },
  };
}

/**
 * Get dashboard configuration based on user role
 */
export function getRoleDashboardConfig(role: string): RoleDashboardConfig {
  const baseConfig = {
    role: role as 'super_admin' | 'admin' | 'agent' | 'user',
    sections: [],
    defaultSection: 'open',
    permissions: {
      canCreateTickets: false,
      canAssignTickets: false,
      canViewAllTickets: false,
      canManageUsers: false,
    },
  };

  switch (role) {
    case 'super_admin':
      return {
        ...baseConfig,
        sections: [
          {
            key: 'new',
            title: 'New Tickets',
            description: 'All unassigned tickets awaiting assignment',
            filterFn: filterNewTicketsForSuperAdmin,
            visible: true,
          },
          {
            key: 'assigned',
            title: 'All Assigned Tickets',
            description: 'All assigned tickets across all admins and agents',
            filterFn: filterAssignedTicketsForSuperAdmin,
            visible: true,
          },
        ],
        permissions: {
          canCreateTickets: true,
          canAssignTickets: true,
          canViewAllTickets: true,
          canManageUsers: true,
        },
      };
    case 'admin':
      return {
        ...baseConfig,
        sections: [
          {
            key: 'new',
            title: 'New Tickets',
            description: 'Tickets created by users awaiting assignment',
            filterFn: filterNewTicketsForAdmins,
            visible: true,
          },
          {
            key: 'assigned',
            title: 'My Assigned Tickets',
            description: 'Tickets you assigned to agents',
            filterFn: filterAssignedTicketsForAdmin,
            visible: true,
          },
        ],
        permissions: {
          canCreateTickets: true,
          canAssignTickets: true,
          canViewAllTickets: false,
          canManageUsers: false,
        },
      };
    case 'agent':
      return {
        ...baseConfig,
        sections: [
          {
            key: 'new',
            title: 'New Tickets',
            description: 'Tickets assigned to you by admins',
            filterFn: filterNewTicketsForAgent,
            visible: true,
          },
          {
            key: 'assigned',
            title: 'My Assigned Tickets',
            description: 'Tickets assigned to you',
            filterFn: filterAssignedTicketsForAgent,
            visible: true,
          },
        ],
        permissions: {
          canCreateTickets: false,
          canAssignTickets: false,
          canViewAllTickets: false,
          canManageUsers: false,
        },
      };
    case 'user':
      return {
        ...baseConfig,
        sections: [
          {
            key: 'my',
            title: 'My Tickets',
            description: 'Tickets you created',
            filterFn: (tickets, context) =>
              filterTicketsByRole(tickets, context).tickets,
            visible: true,
          },
        ],
        permissions: {
          canCreateTickets: true,
          canAssignTickets: false,
          canViewAllTickets: false,
          canManageUsers: false,
        },
      };
    default:
      return baseConfig;
  }
}
