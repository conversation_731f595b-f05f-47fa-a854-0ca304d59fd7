/**
 * Smart Ticket Fetcher Service - 2025 Optimized
 *
 * Efficiently fetches individual tickets when they're not in cache,
 * with proper error handling, rate limiting, and tenant isolation.
 * Designed to work seamlessly with real-time updates and React Query.
 *
 * Key Features:
 * - Rate limiting to prevent API abuse
 * - Tenant isolation and security
 * - Error handling with exponential backoff
 * - Performance monitoring and logging
 * - Integration with existing API patterns
 *
 * <AUTHOR> Augster
 * @version 1.0 - Smart Ticket Fetching (January 2025)
 */

import { Ticket } from '@/features/ticketing/models/ticket.schema';
import { RealtimeDataService } from './realtime-data.service';
import { createServiceSupabaseClient } from '@/lib/supabase-server';

// Rate limiting configuration
interface RateLimitConfig {
  maxRequestsPerMinute: number;
  maxConcurrentRequests: number;
  backoffMultiplier: number;
  maxBackoffMs: number;
}

// Default rate limiting - conservative to prevent API abuse
const DEFAULT_RATE_LIMIT: RateLimitConfig = {
  maxRequestsPerMinute: 30, // Conservative limit for real-time fetching
  maxConcurrentRequests: 5, // Prevent overwhelming the API
  backoffMultiplier: 1.5,
  maxBackoffMs: 10000, // 10 seconds max backoff
};

// Request tracking for rate limiting
interface RequestTracker {
  requests: number[];
  concurrentRequests: Set<string>;
  backoffUntil: number;
}

// Cache for recently fetched tickets to avoid duplicate requests
interface TicketCache {
  [ticketId: string]: {
    ticket: Ticket;
    fetchedAt: number;
    tenantId: string;
  };
}

export class SmartTicketFetcher {
  private static instance: SmartTicketFetcher;
  private requestTracker: RequestTracker;
  private recentCache: TicketCache;
  private config: RateLimitConfig;
  private realtimeDataService: RealtimeDataService;

  private constructor(config: Partial<RateLimitConfig> = {}) {
    this.config = { ...DEFAULT_RATE_LIMIT, ...config };
    this.requestTracker = {
      requests: [],
      concurrentRequests: new Set(),
      backoffUntil: 0,
    };
    this.recentCache = {};

    // Initialize realtime data service for ticket transformation
    const supabase = createServiceSupabaseClient();
    this.realtimeDataService = new RealtimeDataService(supabase);

    // Clean up old requests every minute
    setInterval(() => this.cleanupOldRequests(), 60000);

    // Clean up cache every 5 minutes (keep recent fetches for 5 minutes)
    setInterval(() => this.cleanupCache(), 300000);
  }

  public static getInstance(
    config?: Partial<RateLimitConfig>
  ): SmartTicketFetcher {
    if (!SmartTicketFetcher.instance) {
      SmartTicketFetcher.instance = new SmartTicketFetcher(config);
    }
    return SmartTicketFetcher.instance;
  }

  /**
   * Fetch a single ticket with smart caching and rate limiting
   */
  public async fetchTicket(
    tenantId: string,
    ticketId: string,
    options: {
      skipCache?: boolean;
      priority?: 'high' | 'normal' | 'low';
    } = {}
  ): Promise<Ticket | null> {
    const { skipCache = false, priority = 'normal' } = options;

    try {
      // Check recent cache first (unless explicitly skipped)
      if (!skipCache) {
        const cached = this.getCachedTicket(tenantId, ticketId);
        if (cached) {
          console.log(`📦 Smart Fetcher: Using cached ticket ${ticketId}`);
          return cached;
        }
      }

      // Check rate limits
      if (!this.canMakeRequest(priority)) {
        console.warn(
          `🚫 Smart Fetcher: Rate limit exceeded for ticket ${ticketId}`
        );
        return null;
      }

      // Track the request
      const requestId = `${tenantId}-${ticketId}-${Date.now()}`;
      this.trackRequest(requestId);

      console.log(
        `🔍 Smart Fetcher: Fetching ticket ${ticketId} for tenant ${tenantId}`
      );

      try {
        // Fetch the ticket using the existing API endpoint
        const response = await fetch(
          `/api/tickets/${ticketId}?tenant_id=${tenantId}`,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        if (!response.ok) {
          if (response.status === 404) {
            console.log(`📭 Smart Fetcher: Ticket ${ticketId} not found`);
            return null;
          }
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const ticket = data.data as Ticket;

        // Cache the result
        this.cacheTicket(tenantId, ticketId, ticket);

        console.log(
          `✅ Smart Fetcher: Successfully fetched ticket ${ticketId}`
        );
        return ticket;
      } finally {
        // Always remove from concurrent requests tracking
        this.requestTracker.concurrentRequests.delete(requestId);
      }
    } catch (error) {
      console.error(
        `❌ Smart Fetcher: Error fetching ticket ${ticketId}:`,
        error
      );

      // Implement exponential backoff on errors
      this.applyBackoff();

      return null;
    }
  }

  /**
   * Batch fetch multiple tickets efficiently
   */
  public async fetchTickets(
    tenantId: string,
    ticketIds: string[],
    options: { priority?: 'high' | 'normal' | 'low' } = {}
  ): Promise<Ticket[]> {
    const { priority = 'normal' } = options;

    // Filter out tickets that are already cached
    const uncachedIds = ticketIds.filter(
      (id) => !this.getCachedTicket(tenantId, id)
    );

    if (uncachedIds.length === 0) {
      // All tickets are cached
      return ticketIds
        .map((id) => this.getCachedTicket(tenantId, id))
        .filter((ticket): ticket is Ticket => ticket !== null);
    }

    console.log(
      `🔍 Smart Fetcher: Batch fetching ${uncachedIds.length} tickets`
    );

    // Fetch tickets in parallel with concurrency limit
    const promises = uncachedIds.map((ticketId) =>
      this.fetchTicket(tenantId, ticketId, { priority })
    );

    const results = await Promise.allSettled(promises);

    // Combine cached and newly fetched tickets
    const allTickets: Ticket[] = [];

    for (const ticketId of ticketIds) {
      const cached = this.getCachedTicket(tenantId, ticketId);
      if (cached) {
        allTickets.push(cached);
      }
    }

    // Add successfully fetched tickets
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        allTickets.push(result.value);
      }
    });

    return allTickets;
  }

  /**
   * Check if we can make a request based on rate limits
   */
  private canMakeRequest(priority: 'high' | 'normal' | 'low'): boolean {
    const now = Date.now();

    // Check if we're in backoff period
    if (now < this.requestTracker.backoffUntil) {
      return priority === 'high'; // Only allow high priority during backoff
    }

    // Check concurrent requests
    if (
      this.requestTracker.concurrentRequests.size >=
      this.config.maxConcurrentRequests
    ) {
      return false;
    }

    // Check requests per minute
    const recentRequests = this.requestTracker.requests.filter(
      (timestamp) => now - timestamp < 60000
    );

    return recentRequests.length < this.config.maxRequestsPerMinute;
  }

  /**
   * Track a new request
   */
  private trackRequest(requestId: string): void {
    const now = Date.now();
    this.requestTracker.requests.push(now);
    this.requestTracker.concurrentRequests.add(requestId);
  }

  /**
   * Apply exponential backoff after errors
   */
  private applyBackoff(): void {
    const backoffMs = Math.min(
      1000 *
        Math.pow(
          this.config.backoffMultiplier,
          this.requestTracker.requests.length % 5
        ),
      this.config.maxBackoffMs
    );

    this.requestTracker.backoffUntil = Date.now() + backoffMs;
    console.log(`⏳ Smart Fetcher: Applying backoff for ${backoffMs}ms`);
  }

  /**
   * Get cached ticket if available and fresh
   */
  private getCachedTicket(tenantId: string, ticketId: string): Ticket | null {
    const cached = this.recentCache[ticketId];

    if (!cached || cached.tenantId !== tenantId) {
      return null;
    }

    // Cache is valid for 5 minutes
    const cacheAge = Date.now() - cached.fetchedAt;
    if (cacheAge > 300000) {
      delete this.recentCache[ticketId];
      return null;
    }

    return cached.ticket;
  }

  /**
   * Cache a fetched ticket
   */
  private cacheTicket(
    tenantId: string,
    ticketId: string,
    ticket: Ticket
  ): void {
    this.recentCache[ticketId] = {
      ticket,
      fetchedAt: Date.now(),
      tenantId,
    };
  }

  /**
   * Clean up old request tracking data
   */
  private cleanupOldRequests(): void {
    const now = Date.now();
    this.requestTracker.requests = this.requestTracker.requests.filter(
      (timestamp) => now - timestamp < 60000
    );
  }

  /**
   * Clean up old cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    Object.keys(this.recentCache).forEach((ticketId) => {
      const cached = this.recentCache[ticketId];
      if (now - cached.fetchedAt > 300000) {
        delete this.recentCache[ticketId];
      }
    });
  }

  /**
   * Get current rate limiting status for debugging
   */
  public getRateLimitStatus(): {
    requestsInLastMinute: number;
    concurrentRequests: number;
    isInBackoff: boolean;
    backoffEndsAt: number | null;
  } {
    const now = Date.now();
    const recentRequests = this.requestTracker.requests.filter(
      (timestamp) => now - timestamp < 60000
    );

    return {
      requestsInLastMinute: recentRequests.length,
      concurrentRequests: this.requestTracker.concurrentRequests.size,
      isInBackoff: now < this.requestTracker.backoffUntil,
      backoffEndsAt:
        this.requestTracker.backoffUntil > now
          ? this.requestTracker.backoffUntil
          : null,
    };
  }

  /**
   * Clear all caches (useful for testing or tenant switches)
   */
  public clearCache(): void {
    this.recentCache = {};
    console.log('🗑️ Smart Fetcher: Cache cleared');
  }
}

// Export singleton instance
export const smartTicketFetcher = SmartTicketFetcher.getInstance();
