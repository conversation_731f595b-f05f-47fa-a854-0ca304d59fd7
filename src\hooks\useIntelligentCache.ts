/**
 * useIntelligentCache Hook - 2025 Optimized
 *
 * Provides easy access to intelligent cache management functionality
 * for React components. Integrates seamlessly with React Query and
 * provides optimized cache operations with minimal re-renders.
 *
 * Key Features:
 * - Easy-to-use React hook interface
 * - Automatic performance monitoring
 * - Surgical cache updates
 * - Intelligent ticket reordering
 * - Real-time performance metrics
 *
 * <AUTHOR> Augster
 * @version 1.0 - Intelligent Cache Hook (January 2025)
 */

import { useCallback, useMemo } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { intelligentCacheManager } from '@/lib/services/intelligent-cache-manager.service';
import { Ticket } from '@/features/ticketing/models/ticket.schema';

// Hook return type
interface UseIntelligentCacheReturn {
  // Core cache operations
  updateTicket: (
    queryKey: string[],
    ticket: Ticket,
    options?: {
      operation?: 'insert' | 'update' | 'delete';
      reason?: string;
    }
  ) => boolean;

  // Batch operations
  updateMultipleTickets: (
    queryKey: string[],
    tickets: Ticket[],
    options?: {
      reason?: string;
    }
  ) => boolean[];

  // Performance monitoring
  getPerformanceMetrics: () => ReturnType<
    typeof intelligentCacheManager.getPerformanceMetrics
  >;

  // Configuration
  updateCacheConfig: (
    config: Parameters<typeof intelligentCacheManager.updateConfig>[0]
  ) => void;

  // Utilities
  resetMetrics: () => void;
}

/**
 * Hook for intelligent cache management
 */
export function useIntelligentCache(): UseIntelligentCacheReturn {
  const queryClient = useQueryClient();

  // Memoized update function for single tickets
  const updateTicket = useCallback(
    (
      queryKey: string[],
      ticket: Ticket,
      options: {
        operation?: 'insert' | 'update' | 'delete';
        reason?: string;
      } = {}
    ): boolean => {
      const { operation = 'update', reason = 'manual update' } = options;

      try {
        const success = intelligentCacheManager.updateTicketInCache(
          queryClient,
          queryKey,
          ticket,
          {
            operation,
            ticketId: ticket.id,
            tenantId: ticket.tenantId,
            reason,
          }
        );

        if (success) {
          console.log(
            `🎯 useIntelligentCache: ${operation} for ticket ${ticket.id} completed`
          );
        }

        return success;
      } catch (error) {
        console.error('useIntelligentCache: Error updating ticket:', error);
        return false;
      }
    },
    [queryClient]
  );

  // Memoized batch update function
  const updateMultipleTickets = useCallback(
    (
      queryKey: string[],
      tickets: Ticket[],
      options: {
        reason?: string;
      } = {}
    ): boolean[] => {
      const { reason = 'batch update' } = options;

      try {
        const results = tickets.map((ticket) =>
          updateTicket(queryKey, ticket, { operation: 'update', reason })
        );

        const successCount = results.filter(Boolean).length;
        console.log(
          `🎯 useIntelligentCache: Batch update completed (${successCount}/${tickets.length} successful)`
        );

        return results;
      } catch (error) {
        console.error('useIntelligentCache: Error in batch update:', error);
        return tickets.map(() => false);
      }
    },
    [updateTicket]
  );

  // Memoized performance metrics getter
  const getPerformanceMetrics = useCallback(() => {
    return intelligentCacheManager.getPerformanceMetrics();
  }, []);

  // Memoized config updater
  const updateCacheConfig = useCallback(
    (config: Parameters<typeof intelligentCacheManager.updateConfig>[0]) => {
      intelligentCacheManager.updateConfig(config);
    },
    []
  );

  // Memoized metrics reset
  const resetMetrics = useCallback(() => {
    intelligentCacheManager.resetMetrics();
  }, []);

  // Return memoized object to prevent unnecessary re-renders
  return useMemo(
    () => ({
      updateTicket,
      updateMultipleTickets,
      getPerformanceMetrics,
      updateCacheConfig,
      resetMetrics,
    }),
    [
      updateTicket,
      updateMultipleTickets,
      getPerformanceMetrics,
      updateCacheConfig,
      resetMetrics,
    ]
  );
}

/**
 * Hook for monitoring cache performance in real-time
 */
export function useCachePerformanceMonitor(
  options: {
    enabled?: boolean;
    logInterval?: number; // in milliseconds
  } = {}
) {
  const { enabled = false, logInterval = 30000 } = options; // Default: 30 seconds
  const { getPerformanceMetrics } = useIntelligentCache();

  // Performance monitoring effect
  useMemo(() => {
    if (!enabled) return;

    const interval = setInterval(() => {
      const metrics = getPerformanceMetrics();

      console.group('🎯 Cache Performance Metrics');
      console.log('Total Updates:', metrics.totalUpdates);
      console.log('Surgical Updates:', metrics.surgicalUpdates);
      console.log('Full Reorders:', metrics.fullReorders);
      console.log(
        'Average Update Time:',
        `${metrics.averageUpdateTime.toFixed(2)}ms`
      );
      console.log(
        'Last Update Time:',
        `${metrics.lastUpdateTime.toFixed(2)}ms`
      );
      console.log('Recent Updates (5min):', metrics.recentUpdatesCount);
      console.log('Reorder Rate (1min):', metrics.reorderRate);
      console.log(
        'Surgical Update Ratio:',
        `${((metrics.surgicalUpdates / Math.max(metrics.totalUpdates, 1)) * 100).toFixed(1)}%`
      );
      console.groupEnd();
    }, logInterval);

    return () => clearInterval(interval);
  }, [enabled, logInterval, getPerformanceMetrics]);

  return { getPerformanceMetrics };
}

/**
 * Hook for optimized ticket list updates
 * Specifically designed for ticket list components
 */
export function useOptimizedTicketUpdates(
  tenantId: string,
  options: {
    enableBatchUpdates?: boolean;
    batchDelay?: number; // in milliseconds
  } = {}
) {
  const { enableBatchUpdates = true, batchDelay = 100 } = options;
  const { updateTicket, updateMultipleTickets } = useIntelligentCache();

  // Generate query key for tickets
  const ticketQueryKey = useMemo(
    () => ['tickets', tenantId, 'list'],
    [tenantId]
  );

  // Optimized single ticket update
  const updateSingleTicket = useCallback(
    (ticket: Ticket, reason = 'real-time update') => {
      return updateTicket(ticketQueryKey, ticket, {
        operation: 'update',
        reason,
      });
    },
    [updateTicket, ticketQueryKey]
  );

  // Optimized ticket insertion
  const insertTicket = useCallback(
    (ticket: Ticket, reason = 'new ticket') => {
      return updateTicket(ticketQueryKey, ticket, {
        operation: 'insert',
        reason,
      });
    },
    [updateTicket, ticketQueryKey]
  );

  // Optimized batch updates with optional debouncing
  const updateTicketsBatch = useCallback(
    (tickets: Ticket[], reason = 'batch update') => {
      if (!enableBatchUpdates) {
        return tickets.map((ticket) => updateSingleTicket(ticket, reason));
      }

      // Use batch update for better performance
      return updateMultipleTickets(ticketQueryKey, tickets, { reason });
    },
    [
      enableBatchUpdates,
      updateSingleTicket,
      updateMultipleTickets,
      ticketQueryKey,
    ]
  );

  return useMemo(
    () => ({
      updateSingleTicket,
      insertTicket,
      updateTicketsBatch,
      queryKey: ticketQueryKey,
    }),
    [updateSingleTicket, insertTicket, updateTicketsBatch, ticketQueryKey]
  );
}

/**
 * Hook for cache debugging and development
 */
export function useCacheDebugger(enabled = false) {
  const { getPerformanceMetrics, resetMetrics, updateCacheConfig } =
    useIntelligentCache();

  const debugInfo = useMemo(() => {
    if (!enabled) return null;

    const metrics = getPerformanceMetrics();

    return {
      metrics,
      efficiency: {
        surgicalUpdateRatio:
          (metrics.surgicalUpdates / Math.max(metrics.totalUpdates, 1)) * 100,
        averageUpdateTime: metrics.averageUpdateTime,
        reorderFrequency: metrics.reorderRate,
      },
      recommendations: {
        enableSurgicalUpdates:
          metrics.surgicalUpdates < metrics.totalUpdates * 0.7,
        reduceReorderFrequency: metrics.reorderRate > 5,
        optimizeUpdateTime: metrics.averageUpdateTime > 10,
      },
    };
  }, [enabled, getPerformanceMetrics]);

  const enablePerformanceMode = useCallback(() => {
    updateCacheConfig({
      enableSurgicalUpdates: true,
      enableIntelligentReordering: true,
      maxReorderFrequency: 5,
      performanceLogging: true,
    });
  }, [updateCacheConfig]);

  const enableCompatibilityMode = useCallback(() => {
    updateCacheConfig({
      enableSurgicalUpdates: false,
      enableIntelligentReordering: false,
      maxReorderFrequency: 20,
      performanceLogging: false,
    });
  }, [updateCacheConfig]);

  return useMemo(
    () => ({
      debugInfo,
      resetMetrics,
      enablePerformanceMode,
      enableCompatibilityMode,
    }),
    [debugInfo, resetMetrics, enablePerformanceMode, enableCompatibilityMode]
  );
}
